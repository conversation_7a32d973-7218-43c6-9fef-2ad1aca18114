# Payment API Service Implementation

## Overview

This document describes the comprehensive Zoho Payment API service implementation for the AquaPartner application. The service provides a complete payment processing solution with robust error handling, validation, logging, and TypeScript support.

## Architecture

### Core Components

1. **Payment Service** (`src/app/lib/zohoPaymentService.js`)
   - Main service class handling all Zoho Payment API interactions
   - Token management and refresh functionality
   - Payment session creation and management
   - Refund processing
   - Transaction tracking

2. **API Routes** (`src/app/api/zoho/`)
   - RESTful API endpoints for payment operations
   - Webhook handling for payment status updates
   - Health check and monitoring endpoints

3. **Type Definitions** (`src/types/`)
   - Comprehensive TypeScript types for payment data
   - Service configuration types
   - API response interfaces

4. **Error Handling** (`src/lib/errors.ts`)
   - Custom error classes for different failure scenarios
   - Circuit breaker pattern for external API resilience
   - Retry mechanisms for transient failures

5. **Validation** (`src/lib/validation.ts`)
   - Input validation for all payment requests
   - Data sanitization functions
   - Webhook signature verification

6. **Logging** (`src/lib/logger.ts`)
   - Structured logging with context
   - Performance monitoring
   - Security event tracking

## API Endpoints

### Payment Operations

#### Create Payment Session
```
POST /api/zoho/payments/create
```
Creates a new payment session with Zoho Payments.

**Request Body:**
```json
{
  "amount": 100.00,
  "currency": "INR",
  "description": "Product purchase",
  "invoice_number": "INV-001",
  "customer_id": "CUST-123",
  "customer_name": "John Doe",
  "customer_email": "<EMAIL>",
  "customer_phone": "+91-9876543210",
  "redirect_url": "https://example.com/success",
  "reference_id": "REF-001",
  "meta_data": [
    {"key": "order_id", "value": "ORD-001"}
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment session created successfully",
  "data": {
    "payment_session": {
      "payments_session_id": "ps_123456",
      "amount": 100.00,
      "currency": "INR",
      "status": "created",
      "payment_url": "https://payments.zoho.com/...",
      "expires_at": 1640995200
    },
    "transaction_id": "txn_123456",
    "expires_in": "15 minutes"
  }
}
```

#### Get Payment Status
```
GET /api/zoho/payments/status/{sessionId}
```
Retrieves the current status of a payment session.

#### Update Payment Status
```
PUT /api/zoho/payments/status/{sessionId}
```
Manually updates payment status (for testing/admin purposes).

#### List Transactions
```
GET /api/zoho/payments/list
```
Lists payment transactions with filtering and pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)
- `customer_id`: Filter by customer ID
- `status`: Filter by payment status
- `from_date`: Start date (YYYY-MM-DD)
- `to_date`: End date (YYYY-MM-DD)

#### Advanced Search
```
POST /api/zoho/payments/list
```
Advanced search with complex filters.

**Request Body:**
```json
{
  "customer_ids": ["CUST-123", "CUST-456"],
  "statuses": ["succeeded", "pending"],
  "amount_range": {
    "min": 50.00,
    "max": 500.00
  },
  "date_range": {
    "from": "2024-01-01",
    "to": "2024-12-31"
  },
  "invoice_numbers": ["INV-001", "INV-002"],
  "page": 1,
  "limit": 20,
  "sort_by": "createdAt",
  "sort_order": "desc"
}
```

### Refund Operations

#### Create Refund
```
POST /api/zoho/refunds/create
```
Creates a refund for a successful payment.

**Request Body:**
```json
{
  "payment_id": "pay_123456",
  "amount": 50.00,
  "reason": "Customer request",
  "reference_id": "REF-REFUND-001"
}
```

#### Get Refund Status
```
GET /api/zoho/refunds/status/{refundId}
```
Retrieves the status of a refund.

### Authentication

#### Setup OAuth
```
POST /api/zoho/auth/setup
```
Initial OAuth setup (admin only).

#### Refresh Token
```
POST /api/zoho/auth/refresh
```
Refreshes the access token.

### Webhooks

#### Webhook Endpoint
```
POST /api/zoho/webhooks/payment
```
Receives payment status updates from Zoho.

#### Webhook Management
```
GET /api/zoho/webhooks/manage
POST /api/zoho/webhooks/manage
```
Manages webhook configurations.

#### Test Webhook
```
POST /api/zoho/webhooks/test
```
Tests webhook processing with sample data.

### Health Check

#### Service Health
```
GET /api/zoho/health
```
Comprehensive health check of all service components.

**Response:**
```json
{
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "zoho-payment-service",
  "version": "1.0.0",
  "status": "healthy",
  "checks": {
    "database": {
      "status": "healthy",
      "message": "Connected to MongoDB",
      "response_time": "15ms"
    },
    "zoho_token": {
      "status": "healthy",
      "message": "Token valid",
      "expires_at": "2024-01-02T00:00:00.000Z"
    },
    "zoho_api": {
      "status": "healthy",
      "message": "API accessible",
      "response_time": "250ms"
    }
  },
  "configuration": {
    "account_id": "********",
    "webhook_secret": "configured",
    "domain": "example.com"
  }
}
```

## Error Handling

### Error Types

1. **ValidationError** (400)
   - Invalid input data
   - Missing required fields
   - Format validation failures

2. **AuthenticationError** (401)
   - Invalid or expired tokens
   - Missing authentication

3. **AuthorizationError** (403)
   - Insufficient permissions
   - Access denied

4. **NotFoundError** (404)
   - Payment session not found
   - Transaction not found

5. **ConflictError** (409)
   - Duplicate payment attempts
   - Invalid state transitions

6. **RateLimitError** (429)
   - Too many requests
   - Rate limit exceeded

7. **ZohoApiError** (500/502/503)
   - Zoho API failures
   - External service errors

8. **DatabaseError** (500)
   - Database connection issues
   - Query failures

### Error Response Format

```json
{
  "error": "VALIDATION_ERROR",
  "message": "Validation failed for field 'amount': must be a positive number",
  "details": {
    "field": "amount",
    "value": -10,
    "constraint": "must be a positive number"
  },
  "retryable": false
}
```

## Security Features

### Input Validation
- Comprehensive validation for all input fields
- Data sanitization to prevent injection attacks
- Type checking and format validation

### Webhook Security
- HMAC signature verification
- Replay attack prevention
- IP whitelist support (configurable)

### Authentication
- OAuth 2.0 with Zoho
- Automatic token refresh
- Secure token storage

### Rate Limiting
- Configurable rate limits per endpoint
- IP-based and user-based limiting
- Graceful degradation

## Monitoring and Logging

### Structured Logging
- JSON-formatted logs
- Contextual information (payment IDs, customer IDs)
- Performance metrics
- Security events

### Health Monitoring
- Database connectivity checks
- External API availability
- Token validity monitoring
- Webhook processing status

### Metrics
- Payment success/failure rates
- Processing times
- Error rates by type
- Volume metrics

## Configuration

### Environment Variables

```bash
# Database
MONGODB_URI=mongodb://localhost:27017/aquapartner

# Zoho OAuth
ZOHO_OAUTH_CLIENT_ID=your_client_id
ZOHO_OAUTH_CLIENT_SECRET=your_client_secret
ZOHO_OAUTH_REFRESH_TOKEN=your_refresh_token

# Zoho Payments
ZOHO_PAYMENT_SESSION_URL=https://payments.zoho.com/api/v1
ZOHO_PAY_ACCOUNT_ID=your_account_id
ZOHO_PAY_API_KEY=your_api_key

# Webhooks
ZOHO_WEBHOOK_SECRET=your_webhook_secret

# Application
NEXT_PUBLIC_DOMAIN=https://your-domain.com
NEXT_PUBLIC_API_DOMAIN=https://api.your-domain.com

# Logging
LOG_LEVEL=info
```

## Database Models

### PaymentTransaction
- Stores all payment session and transaction data
- Includes customer information and metadata
- Tracks status changes and timestamps

### PaymentAccessToken
- Manages OAuth tokens
- Automatic refresh functionality
- Expiration tracking

### WebhookEvent
- Logs all webhook events
- Processing status tracking
- Retry mechanism for failed processing

## Testing

### Unit Tests
- Service method testing
- Validation function testing
- Error handling verification

### Integration Tests
- End-to-end payment flow
- Webhook processing
- Database operations

### Load Testing
- Performance under high load
- Rate limiting verification
- Circuit breaker testing

## Deployment Considerations

### Production Checklist
- [ ] Environment variables configured
- [ ] Database indexes created
- [ ] Webhook endpoints registered with Zoho
- [ ] Rate limiting configured
- [ ] Monitoring alerts set up
- [ ] Log aggregation configured
- [ ] SSL certificates installed
- [ ] Security headers configured

### Scaling
- Horizontal scaling support
- Database connection pooling
- Caching for frequently accessed data
- Load balancer configuration

## Maintenance

### Regular Tasks
- Token refresh monitoring
- Database cleanup of old transactions
- Log rotation and archival
- Performance metric review

### Updates
- Zoho API version updates
- Security patch application
- Feature enhancements
- Bug fixes

## Support

### Troubleshooting
- Check service health endpoint
- Review application logs
- Verify environment configuration
- Test webhook connectivity

### Common Issues
- Token expiration
- Network connectivity
- Database connection issues
- Webhook signature verification failures

For additional support, refer to the Zoho Payments API documentation and the application logs.
