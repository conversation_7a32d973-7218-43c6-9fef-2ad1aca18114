// import connectedDB from '@/config/database'
// import InvoiceItems from '@/models/InvoiceItems'

import connectedDB from '@/app/config/database'
import InvoiceItems from '@/app/models/InvoiceItems'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { id: 'customer1' },
    { id: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/invoiceItems/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const invoiceItems = await InvoiceItems.find({
      invoiceId: params.id,
    })

    // console.log(invoiceItems);
    if (!invoiceItems) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify(invoiceItems), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
