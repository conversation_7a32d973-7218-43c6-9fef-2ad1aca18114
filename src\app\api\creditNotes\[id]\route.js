import connectedDB from '@/app/config/database'
import CreditNotes from '@/app/models/CreditNotes'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { id: 'customer1' },
    { id: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/creditNotes/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const creditNotes = await CreditNotes.find({
      customerId: params.id,
    }).sort({ date1: -1 })

    if (!creditNotes) return new Response(JSON.stringify({ results: [] }), { status: 200 })
    return new Response(JSON.stringify({ results: creditNotes }), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
