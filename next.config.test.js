/** @type {import('next').NextConfig} */
const nextConfig = {
  // Explicitly set to NOT use static export
  output: 'standalone',  // Use 'standalone' instead of 'export'
  reactStrictMode: false,
  productionBrowserSourceMaps: false,
  webpack: (config, { dev }) => {
    if (dev) {
      config.devtool = false
    }
    return config
  },
  experimental: {
    missingSuspenseWithCSRBailout: false,
  },
}

module.exports = nextConfig