/**
 * Live Payment API Test - Production Environment
 * 
 * This script attempts to create an actual payment session in production
 */

const fetch = globalThis.fetch || require('node-fetch')

const PRODUCTION_URL = 'https://yellow-sky-08e56d200.5.azurestaticapps.net'

async function testLivePaymentCreation() {
  console.log('🚀 Live Payment API Test - Production Environment')
  console.log('================================================')
  console.log(`URL: ${PRODUCTION_URL}`)
  console.log(`Time: ${new Date().toISOString()}`)
  console.log('================================================\n')

  // Test payment data - using safe ₹1 amount for production
  const paymentData = {
    amount: 1.0,
    currency: 'INR',
    description: 'Live Production Test Payment - Safe Amount',
    invoice_number: `LIVE_TEST_${Date.now()}`,
    customer_id: `live_customer_${Date.now()}`,
    customer_name: 'Live Test Customer',
    customer_email: '<EMAIL>',
    customer_phone: '+91-9876543210',
    redirect_url: `${PRODUCTION_URL}/payment/success`,
    reference_id: `LIVE_REF_${Date.now()}`,
    meta_data: [
      { key: 'test_type', value: 'live_production_test' },
      { key: 'amount_safe', value: 'true' },
      { key: 'environment', value: 'production' }
    ]
  }

  console.log('📋 Payment Data to Send:')
  console.log(JSON.stringify(paymentData, null, 2))
  console.log('\n🔄 Sending POST request to create payment session...\n')

  try {
    const startTime = Date.now()
    
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(paymentData)
    })

    const endTime = Date.now()
    const responseTime = endTime - startTime

    console.log(`⏱️  Response Time: ${responseTime}ms`)
    console.log(`📊 HTTP Status: ${response.status}`)
    console.log(`📋 Response Headers:`)
    
    // Log important headers
    const headers = Object.fromEntries(response.headers.entries())
    console.log(`   Content-Type: ${headers['content-type'] || 'Not set'}`)
    console.log(`   Content-Length: ${headers['content-length'] || 'Not set'}`)
    
    // Get response text
    const responseText = await response.text()
    console.log(`\n📄 Raw Response (first 500 chars):`)
    console.log(responseText.substring(0, 500) + (responseText.length > 500 ? '...' : ''))

    // Try to parse as JSON
    let responseData
    try {
      responseData = JSON.parse(responseText)
      console.log('\n✅ Response is valid JSON')
    } catch (parseError) {
      console.log('\n❌ Response is not valid JSON:', parseError.message)
      return { success: false, error: 'Invalid JSON response' }
    }

    // Analyze the response
    console.log('\n🔍 Response Analysis:')
    
    if (responseData.success && responseData.data && responseData.data.payment_session_id) {
      // SUCCESS - Payment session created
      console.log('🎉 SUCCESS: Payment session created!')
      console.log(`💳 Payment Session ID: ${responseData.data.payment_session_id}`)
      console.log(`💰 Amount: ₹${responseData.data.amount}`)
      console.log(`🏷️  Invoice: ${responseData.data.invoice_number}`)
      console.log(`⏰ Created: ${new Date(responseData.data.created_time * 1000).toISOString()}`)
      
      if (responseData.payment_session && responseData.payment_session.payment_url) {
        console.log(`🔗 Payment URL: ${responseData.payment_session.payment_url}`)
      }
      
      return {
        success: true,
        payment_session_id: responseData.data.payment_session_id,
        amount: responseData.data.amount,
        currency: responseData.data.currency,
        payment_url: responseData.payment_session?.payment_url,
        response_time: responseTime
      }
      
    } else if (responseData.message && responseData.message.includes('Payment Session Creation Requirements')) {
      // ROUTING ISSUE - Getting documentation instead of payment session
      console.log('⚠️  ROUTING ISSUE: Received documentation instead of payment session')
      console.log('   This means POST request is being handled as GET request')
      console.log('   Azure Static Web Apps routing configuration needs to be fixed')
      
      return {
        success: false,
        error: 'routing_issue',
        message: 'POST request returning GET response (documentation)',
        response_time: responseTime
      }
      
    } else if (responseData.error) {
      // API ERROR - Payment creation failed
      console.log('❌ API ERROR: Payment session creation failed')
      console.log(`   Error: ${responseData.error}`)
      console.log(`   Message: ${responseData.message || 'No message provided'}`)
      
      return {
        success: false,
        error: responseData.error,
        message: responseData.message,
        response_time: responseTime
      }
      
    } else {
      // UNKNOWN RESPONSE
      console.log('❓ UNKNOWN RESPONSE: Unexpected response format')
      console.log('   Response does not match expected success or error patterns')
      
      return {
        success: false,
        error: 'unknown_response',
        response_data: responseData,
        response_time: responseTime
      }
    }

  } catch (error) {
    console.log('💥 REQUEST FAILED:', error.message)
    
    if (error.name === 'AbortError') {
      console.log('   Reason: Request timeout')
    } else if (error.message.includes('fetch')) {
      console.log('   Reason: Network error or connection issue')
    } else {
      console.log('   Reason: Unknown error')
    }
    
    return {
      success: false,
      error: 'request_failed',
      message: error.message
    }
  }
}

// Run the test
if (typeof require !== 'undefined' && require.main === module) {
  testLivePaymentCreation()
    .then(result => {
      console.log('\n' + '='.repeat(50))
      console.log('🏁 LIVE PAYMENT TEST COMPLETED')
      console.log('='.repeat(50))
      
      if (result.success) {
        console.log('✅ Result: SUCCESS - Payment session created in production!')
        console.log(`💳 Session ID: ${result.payment_session_id}`)
        process.exit(0)
      } else {
        console.log('❌ Result: FAILED - Unable to create payment session')
        console.log(`🔍 Error: ${result.error}`)
        console.log(`📝 Message: ${result.message || 'No additional details'}`)
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('\n💥 Test execution failed:', error.message)
      process.exit(1)
    })
}

module.exports = { testLivePaymentCreation }
