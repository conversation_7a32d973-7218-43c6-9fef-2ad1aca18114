//import connectedDB from '../../../lib/mongodb'
import connectedDB from '@/app/config/database'
import { withAuth } from '../../../lib/auth'

export async function generateStaticParams() {
  // Return all possible user IDs that this route should handle
  // For example:
  return [
    { id: 'user1' },
    { id: 'user2' },
    // Add all other user IDs you need to support
  ]
}

// GET /api/users/[id]
export const GET = async (request, { params }) => {
  try {
    // Authenticate the request
    const { authenticated, uid, error } = await withAuth(request)

    // If not authenticated, return error
    if (!authenticated) {
      return new Response(JSON.stringify({ error: error || 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    // Security check: users can only access their own data
    if (params.id !== uid) {
      return new Response(JSON.stringify({ error: 'Unauthorized access' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    // Connect to the database
    await connectedDB()

    // Fetch user data
    /* const userData = await InvoicesPayload.find({ userId: uid }).limit(10)

    if (!userData || userData.length === 0) {
      return new Response(JSON.stringify({ error: 'User data not found' }), { 
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    } */

    return new Response(JSON.stringify({ name: 'venkat1', email: '<EMAIL>' }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error(error)
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
}

// PUT /api/users/[id]
export const PUT = async (request, { params }) => {
  try {
    // Authenticate the request
    const { authenticated, uid, error } = await withAuth(request)

    // If not authenticated, return error
    if (!authenticated) {
      return new Response(JSON.stringify({ error: error || 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    // Security check: users can only update their own data
    if (params.id !== uid) {
      return new Response(JSON.stringify({ error: 'Unauthorized access' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    // Connect to the database
    await connectedDB()

    // Parse the request body
    const body = await request.json()

    // Update the user data
    const result = await InvoicesPayload.updateOne({ userId: uid }, { $set: { ...body, updatedAt: new Date() } })

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error(error)
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
}
