import connectedDB from '@/app/config/database'
import Customer from '@/app/models/Customer'
import CustomerAddress from '@/app/models/CustomerAddress'
import TestRetailers from '@/app/models/TestRetailers'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { phno: 'customer1' },
    { phno: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/validateRetailer/:phno
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const phno = params?.phno?.slice(-10)
    const regex = new RegExp(phno + '$')

    // Function to construct the result object
    const constructResult = async (data) => {
      const billingAddress = await CustomerAddress.findOne({
        customerId: data.customerId,
      })
      return {
        customerId: data.customerId,
        customerName: data.customerName,
        email: data.Email,
        mobileNumber: data.mobileNumber,
        companyName: data.companyName,
        gstNo: data.gstNo,
        businessVertical: data.businessVertical,
        customerCode: data.customerCode,
        billingAddress: billingAddress?.billingAddress || '',
      }
    }

    // Check in TestRetailers
    let result = await TestRetailers.findOne({ mobileNumber: { $regex: regex } })
    if (result) {
      result = await constructResult(result)
      return new Response(JSON.stringify({ result }), { status: 200 })
    }

    // Check in Customer
    result = await Customer.findOne({ mobileNumber: { $regex: regex } })
    if (result) {
      result = await constructResult(result)
      return new Response(JSON.stringify({ result }), { status: 200 })
    }

    // No match found
    return new Response(JSON.stringify({ result: null }), { status: 200 })
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Internal Server Error', details: error.message }), { status: 500 })
  }
}
