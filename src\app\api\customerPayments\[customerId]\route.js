import connectedDB from '@/app/config/database'
import CustomerPayments from '@/app/models/Transactions'
import { parseCurrency } from '@/utils/formats'
import _ from 'lodash'
import moment from 'moment'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { customerId: 'customer1' },
    { customerId: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/customerPayments/:customerId
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const customerPayments = await CustomerPayments.find({
      customerId: `${params.customerId}`,
    })

    // Sort by paymentsDate using lodash
    const sortedPayments = _.orderBy(
      customerPayments,
      [(payment) => moment(payment.paymentsDate, 'DD MMM YYYY').toDate()], // Sort key (paymentsDate converted to Date)
      ['desc'] // Sorting order
    )

    // Use lodash to sum the total values
    const totalSum = _.sumBy(sortedPayments, (payment) => parseCurrency(payment.amount))

    if (!sortedPayments) return new Response(JSON.stringify({ totalSum: 0, results: [] }), { status: 200 })
    return new Response(JSON.stringify({ totalSum: totalSum, results: sortedPayments }), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
