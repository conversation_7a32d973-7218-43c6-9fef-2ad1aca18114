//import connectedDB from '../../lib/mongodb'
import connectedDB from '@/app/config/database'
import { withAuth } from '../../lib/auth'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { id: 'customer1' },
    { id: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// POST /api/syncInvoices
export const POST = async (request) => {
  try {
    // Authenticate the request
    const { authenticated, uid, error } = await withAuth(request)

    // If not authenticated, return error
    if (!authenticated) {
      return new Response(JSON.stringify({ error: error || 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    // Connect to the database
    await connectedDB()

    // Parse the JSON body
    const body = await request.json()

    // Optional: Add user ID to the payload for security
    body.userId = uid

    // If you want to fetch invoices based on userId for security
    // Only return invoices belonging to the authenticated user

    // Respond with success
    return new Response(
      JSON.stringify({
        message: 'Data received successfully',
        data: 'Data from Server',
      }),
      {
        status: 201,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error(error)
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
}

// GET /api/users
export const GET = async (request) => {
  try {
    // Authenticate the request
    const { authenticated, uid, error } = await withAuth(request)

    // If not authenticated, return error
    if (!authenticated) {
      return new Response(JSON.stringify({ error: error || 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    // Connect to the database
    await connectedDB()

    // Fetch user data (only for the authenticated user)
    /* const userData = await InvoicesPayload.find({ userId: uid }).limit(10)

    if (!userData || userData.length === 0) {
      return new Response(JSON.stringify({ error: 'User data not found' }), { 
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    } */

    return new Response(JSON.stringify({ name: 'Venkat', email: '<EMAIL>' }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error(error)
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
}
