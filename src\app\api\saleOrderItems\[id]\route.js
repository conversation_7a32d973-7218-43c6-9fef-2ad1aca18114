import connectedDB from '@/app/config/database'
import SalesOrderItems from '@/app/models/SaleOrderItems'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { id: 'customer1' },
    { id: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/saleOrderItems/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const saleOrderItems = await SalesOrderItems.find({
      salesOrderId: params.id,
    })

    // console.log(saleOrderItems);
    if (!saleOrderItems) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify(saleOrderItems), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
