# Fix Guide: Input Validation Middleware Issue

## Problem
Production environment accepts invalid payment data without validation (returns 200 instead of 400).

## Root Cause Analysis

The validation middleware is not active in production because POST requests aren't reaching the proper handler due to the routing issue.

### Step 1: Verify Current Validation Logic
```bash
# Check the payment creation route file
cat src/app/api/zoho/payments/create-session/route.js
```

Look for validation logic in the POST handler.

### Step 2: Test Validation Locally
```bash
# Test with invalid data locally
curl -X POST http://localhost:3000/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{"amount": -100, "currency": "INVALID"}'
```

Expected: Status 400 with error message

## Solution Steps

### Step 1: Enhance Validation in Route Handler

Update `src/app/api/zoho/payments/create-session/route.js`:

```javascript
import zohoPaymentService from '@/app/lib/zohoPaymentService'

// Validation helper function
function validatePaymentData(data) {
  const errors = []
  
  // Required fields validation
  if (!data.amount) errors.push('amount is required')
  if (!data.description) errors.push('description is required')
  if (!data.invoice_number) errors.push('invoice_number is required')
  if (!data.customer_id) errors.push('customer_id is required')
  
  // Data type validation
  if (data.amount && (typeof data.amount !== 'number' || data.amount <= 0)) {
    errors.push('amount must be a positive number')
  }
  
  // Currency validation
  if (data.currency && !['INR', 'USD', 'EUR'].includes(data.currency)) {
    errors.push('currency must be INR, USD, or EUR')
  }
  
  // String length validation
  if (data.description && data.description.length > 500) {
    errors.push('description must be 500 characters or less')
  }
  
  if (data.invoice_number && data.invoice_number.length > 50) {
    errors.push('invoice_number must be 50 characters or less')
  }
  
  return errors
}

export async function POST(request) {
  try {
    // Parse request body
    let body
    try {
      body = await request.json()
    } catch (parseError) {
      return new Response(JSON.stringify({
        error: 'Invalid JSON',
        message: 'Request body must be valid JSON'
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    
    // Validate input data
    const validationErrors = validatePaymentData(body)
    if (validationErrors.length > 0) {
      return new Response(JSON.stringify({
        error: 'Validation failed',
        message: 'Invalid input data',
        details: validationErrors
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    
    // Process payment creation
    const result = await zohoPaymentService.createPaymentSession(body)
    
    return new Response(JSON.stringify({
      success: true,
      message: 'Payment session created successfully',
      data: result
    }), { 
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('Payment creation error:', error)
    
    return new Response(JSON.stringify({
      error: 'Payment creation failed',
      message: error.message
    }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}
```

### Step 2: Add Validation to ZohoPaymentService

Update `src/app/lib/zohoPaymentService.js` to include validation:

```javascript
// Add validation method to ZohoPaymentService class
validatePaymentData(paymentData) {
  const {
    amount,
    currency = 'INR',
    description,
    invoice_number,
    customer_id,
    customer_email,
    customer_phone
  } = paymentData

  // Required fields validation
  if (!amount || !description || !invoice_number || !customer_id) {
    throw new Error('Missing required fields: amount, description, invoice_number, customer_id')
  }

  // Amount validation
  if (typeof amount !== 'number' || amount <= 0) {
    throw new Error('Amount must be a positive number')
  }

  // Email validation (if provided)
  if (customer_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customer_email)) {
    throw new Error('Invalid email format')
  }

  // Phone validation (if provided)
  if (customer_phone && !/^\+?[\d\s\-\(\)]+$/.test(customer_phone)) {
    throw new Error('Invalid phone number format')
  }

  return true
}

// Update createPaymentSession method
async createPaymentSession(paymentData) {
  try {
    // Validate input data
    this.validatePaymentData(paymentData)
    
    const accessToken = await this.getValidAccessToken()
    
    // ... rest of existing code
  } catch (error) {
    console.error('Payment session creation error:', error)
    throw error
  }
}
```

### Step 3: Create Validation Middleware (Optional)

Create `src/app/lib/validation.js`:

```javascript
export function validatePaymentRequest(request) {
  return async function(handler) {
    try {
      const body = await request.json()
      
      // Validation logic here
      const errors = validatePaymentData(body)
      
      if (errors.length > 0) {
        return new Response(JSON.stringify({
          error: 'Validation failed',
          details: errors
        }), { status: 400 })
      }
      
      return handler(body)
    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Invalid request'
      }), { status: 400 })
    }
  }
}
```

## Testing Procedures

### Test 1: Invalid Amount
```bash
curl -X POST http://localhost:3000/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{"amount": -100, "currency": "INR"}'
```
Expected: Status 400 with validation error

### Test 2: Missing Required Fields
```bash
curl -X POST http://localhost:3000/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{"amount": 100}'
```
Expected: Status 400 with missing fields error

### Test 3: Invalid Currency
```bash
curl -X POST http://localhost:3000/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{"amount": 100, "currency": "INVALID", "description": "test", "invoice_number": "test", "customer_id": "test"}'
```
Expected: Status 400 with currency validation error

### Test 4: Valid Data
```bash
curl -X POST http://localhost:3000/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100,
    "currency": "INR",
    "description": "Valid test payment",
    "invoice_number": "VALID_001",
    "customer_id": "valid_customer"
  }'
```
Expected: Status 201 with payment session data

### Test 5: Production Validation
```bash
# Test production after deployment
curl -X POST https://yellow-sky-08e56d200.5.azurestaticapps.net/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{"amount": -100, "currency": "INVALID"}'
```
Expected: Status 400 with validation error

## Verification Script

Create `tests/validation-test.js`:
```javascript
const fetch = require('node-fetch')

async function testValidation() {
  const tests = [
    {
      name: 'Negative Amount',
      data: { amount: -100 },
      expectedStatus: 400
    },
    {
      name: 'Missing Fields',
      data: { amount: 100 },
      expectedStatus: 400
    },
    {
      name: 'Invalid Currency',
      data: { amount: 100, currency: 'INVALID' },
      expectedStatus: 400
    },
    {
      name: 'Valid Data',
      data: {
        amount: 100,
        currency: 'INR',
        description: 'Test',
        invoice_number: 'TEST_001',
        customer_id: 'test'
      },
      expectedStatus: 201
    }
  ]

  for (const test of tests) {
    const response = await fetch('http://localhost:3000/api/zoho/payments/create-session', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(test.data)
    })
    
    console.log(`${test.name}: ${response.status === test.expectedStatus ? '✅' : '❌'} (${response.status})`)
  }
}

testValidation()
```

## Rollback Procedure

If validation causes issues:

### Step 1: Disable Strict Validation
```javascript
// Temporarily comment out validation
// const validationErrors = validatePaymentData(body)
// if (validationErrors.length > 0) { ... }
```

### Step 2: Revert to Previous Version
```bash
git revert HEAD
git push origin main
```

## Success Criteria
- Invalid data returns status 400 with error details
- Valid data returns status 201 with payment session
- Production and local validation behavior match
- Error messages are clear and helpful
