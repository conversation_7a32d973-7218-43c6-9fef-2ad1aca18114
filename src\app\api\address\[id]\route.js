import connectedDB from '@/app/config/database'
import CustomerAddress from '@/app/models/CustomerAddress'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { id: 'customer1' },
    { id: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/address/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const address = await CustomerAddress.findOne({
      customerId: params.id,
    })

    if (!address) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify(address), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
