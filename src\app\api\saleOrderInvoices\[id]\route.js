import connectedDB from '@/app/config/database'
import SaleOrderInvoices from '@/app/models/SaleOrderInvoices'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { id: 'customer1' },
    { id: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/saleOrderInvoices/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const saleOrderInvoices = await SaleOrderInvoices.find({
      salesOrderId: params.id,
    })

    // console.log(saleOrderInvoices);
    if (!saleOrderInvoices) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify(saleOrderInvoices), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
