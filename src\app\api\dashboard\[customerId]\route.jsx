//import connectedDB from '@/config/database';
import connectedDB from '@/app/config/database'
import Dashboard from '@/app/models/Dashboard'
import { getCurrentFinancialYearEnd, getCurrentFinancialYearStart } from '@/utils/date'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { customerId: 'customer1' },
    { customerId: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

function filterFinancialYearData(dashboard) {
  const currentFinancialYear = getCurrentFinancialYearStart()
  const nextFinancialYear = getCurrentFinancialYearEnd()
  const financialYearMonths = ['04', '05', '06', '07', '08', '09', '10', '11', '12', '01', '02', '03']
  const currentMonth = new Date().getMonth() + 1 // JavaScript months are 0-indexed
  const currentYearForMonth = new Date().getFullYear()
  const currentFinancialYearStartMonthIndex = 0 // Index of April in financialYearMonths
  const currentFinancialYearEndMonthIndex = 9 // Index after December

  const filteredSales = {}
  const filteredPayments = {}

  const populateMissingMonthsCurrentYear = (existingData, isSales) => {
    const populatedData = {}

    for (let i = currentFinancialYearStartMonthIndex; i < currentFinancialYearEndMonthIndex; i++) {
      const monthCode = financialYearMonths[i]
      const monthNumber = parseInt(monthCode, 10)

      if (monthNumber > currentMonth || currentYearForMonth !== parseInt(currentFinancialYear, 10)) {
        break // Stop populating if we've gone past the current month of the current year
      }

      const existingMonthData =
        Array.isArray(existingData) && existingData.find((item) => Object.keys(item)[0] === monthCode)

      if (existingMonthData) {
        populatedData[monthCode] = existingMonthData[monthCode]
      } else {
        populatedData[monthCode] = { [isSales ? 'totalSales' : 'totalPayment']: 0, weeks: [] }
      }
    }
    return Object.keys(populatedData).length > 0 ? [populatedData] : []
  }

  const filterExistingData = (existingData, startMonthIndex, endMonthIndex) => {
    const filtered = []
    if (Array.isArray(existingData)) {
      for (const monthData of existingData) {
        const monthCode = Object.keys(monthData)[0]
        const monthIndex = financialYearMonths.indexOf(monthCode)
        if (monthIndex >= startMonthIndex && monthIndex < endMonthIndex) {
          filtered.push(monthData)
        }
      }
    } else if (existingData instanceof Map) {
      for (const [year, months] of existingData.entries()) {
        if (year === currentFinancialYear || year === nextFinancialYear) {
          const filteredMonths = Array.from(months).filter((monthData) => {
            const monthCode = Object.keys(monthData)[0]
            const monthIndex = financialYearMonths.indexOf(monthCode)
            return monthIndex >= startMonthIndex && monthIndex < endMonthIndex
          })
          if (filteredMonths.length > 0) {
            filtered[year] = filteredMonths // Might need to adjust based on desired output structure
          }
        }
      }
    }
    return filtered
  }

  // Handle Sales Data
  if (typeof dashboard.sales === 'object' && dashboard.sales !== null) {
    filteredSales[currentFinancialYear] = populateMissingMonthsCurrentYear(dashboard.sales[currentFinancialYear], true)
    filteredSales[nextFinancialYear] = filterExistingData(dashboard.sales[nextFinancialYear], 9, 12) // Jan to Mar
    if (filteredSales[currentFinancialYear].length === 0 && !dashboard.sales[currentFinancialYear])
      delete filteredSales[currentFinancialYear]
    if (filteredSales[nextFinancialYear].length === 0 && !dashboard.sales[nextFinancialYear])
      delete filteredSales[nextFinancialYear]
  } else if (dashboard.sales instanceof Map) {
    const salesCurrentYear = Array.from(dashboard.sales.get(currentFinancialYear) || [])
    const salesNextYear = Array.from(dashboard.sales.get(nextFinancialYear) || [])
    filteredSales[currentFinancialYear] = populateMissingMonthsCurrentYear(salesCurrentYear, true)
    // For the next year, only include existing data
    const filteredNextYearSales = []
    salesNextYear.forEach((monthData) => {
      const monthCode = Object.keys(monthData)[0]
      if (financialYearMonths.slice(9).includes(monthCode)) {
        filteredNextYearSales.push(monthData)
      }
    })
    if (filteredNextYearSales.length > 0) {
      filteredSales[nextFinancialYear] = filteredNextYearSales
    }
    if (Object.keys(filteredSales[currentFinancialYear]).length === 0 && !dashboard.sales.get(currentFinancialYear))
      delete filteredSales[currentFinancialYear]
    if (Object.keys(filteredSales[nextFinancialYear]).length === 0 && !dashboard.sales.get(nextFinancialYear))
      delete filteredSales[nextFinancialYear]
  } else {
    filteredSales[currentFinancialYear] = populateMissingMonthsCurrentYear([], true)
  }

  // Handle Payments Data
  if (typeof dashboard.payments === 'object' && dashboard.payments !== null) {
    filteredPayments[currentFinancialYear] = populateMissingMonthsCurrentYear(
      dashboard.payments[currentFinancialYear],
      false
    )
    filteredPayments[nextFinancialYear] = filterExistingData(dashboard.payments[nextFinancialYear], 9, 12) // Jan to Mar
    if (filteredPayments[currentFinancialYear].length === 0 && !dashboard.payments[currentFinancialYear])
      delete filteredPayments[currentFinancialYear]
    if (filteredPayments[nextFinancialYear].length === 0 && !dashboard.payments[nextFinancialYear])
      delete filteredPayments[nextFinancialYear]
  } else if (dashboard.payments instanceof Map) {
    const paymentsCurrentYear = Array.from(dashboard.payments.get(currentFinancialYear) || [])
    const paymentsNextYear = Array.from(dashboard.payments.get(nextFinancialYear) || [])
    filteredPayments[currentFinancialYear] = populateMissingMonthsCurrentYear(paymentsCurrentYear, false)
    // For the next year, only include existing data
    const filteredNextYearPayments = []
    paymentsNextYear.forEach((monthData) => {
      const monthCode = Object.keys(monthData)[0]
      if (financialYearMonths.slice(9).includes(monthCode)) {
        filteredNextYearPayments.push(monthData)
      }
    })
    if (filteredNextYearPayments.length > 0) {
      filteredPayments[nextFinancialYear] = filteredNextYearPayments
    }
    if (
      Object.keys(filteredPayments[currentFinancialYear]).length === 0 &&
      !dashboard.payments.get(currentFinancialYear)
    )
      delete filteredPayments[currentFinancialYear]
    if (Object.keys(filteredPayments[nextFinancialYear]).length === 0 && !dashboard.payments.get(nextFinancialYear))
      delete filteredPayments[nextFinancialYear]
  } else {
    filteredPayments[currentFinancialYear] = populateMissingMonthsCurrentYear([], false)
  }

  return { ...dashboard._doc, sales: filteredSales, payments: filteredPayments }
}

export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const dashboard = await Dashboard.findOne({
      customerId: params.customerId,
    }).lean() // Use .lean() to get a plain JavaScript object

    if (!dashboard) {
      return new Response(JSON.stringify([]), { status: 200 })
    }

    const filteredDashboard = filterFinancialYearData(dashboard)
    dashboard.payments = filteredDashboard.payments
    dashboard.sales = filteredDashboard.sales

    return new Response(JSON.stringify(dashboard), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
