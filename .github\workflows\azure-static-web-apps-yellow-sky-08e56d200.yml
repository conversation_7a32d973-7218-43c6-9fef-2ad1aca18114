name: Azure Static Web Apps CI/CD

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches:
      - main

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
          lfs: false
      - name: Build And Deploy
        id: builddeploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_YELLOW_SKY_08E56D200 }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for Github integrations (i.e. PR comments)
          action: 'upload'
          ###### Repository/Build Configurations - These values can be configured to match your app requirements. ######
          # For more information regarding Static Web App workflow configurations, please visit: https://aka.ms/swaworkflowconfig
          app_location: '/' # App source code path
          api_location: '/api' # Api source code path - optional
          output_location: '' # Built app content directory - optional
          ###### End of Repository/Build Configurations ######
        env:
          # Domain Configuration
          NEXT_PUBLIC_DOMAIN: ${{ secrets.NEXT_PUBLIC_DOMAIN }}
          NEXT_PUBLIC_API_DOMAIN: ${{ secrets.NEXT_PUBLIC_API_DOMAIN }}

          # Database Configuration
          MONGODB_URI: ${{ secrets.MONGODB_URI }}

          # Zoho Payment Configuration
          ZOHO_PAYMENT_SESSION_URL: ${{ secrets.ZOHO_PAYMENT_SESSION_URL }}
          ZOHO_PAY_ACCOUNT_ID: ${{ secrets.ZOHO_PAY_ACCOUNT_ID }}
          ZOHO_PAY_API_KEY: ${{ secrets.ZOHO_PAY_API_KEY }}

          # Webhook Configuration
          ZOHO_WEBHOOK_SECRET: ${{ secrets.ZOHO_WEBHOOK_SECRET }}

  close_pull_request_job:
    if: github.event_name == 'pull_request' && github.event.action == 'closed'
    runs-on: ubuntu-latest
    name: Close Pull Request Job
    steps:
      - name: Close Pull Request
        id: closepullrequest
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_YELLOW_SKY_08E56D200 }}
          action: 'close'
