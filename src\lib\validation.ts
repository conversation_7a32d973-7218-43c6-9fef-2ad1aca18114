// Validation utilities for payment service

import { ValidationError, createValidationError } from './errors'
import type { PaymentRequest, RefundRequest, PaymentStatus } from '../types/payment'

// Basic validation functions
export const isRequired = (value: any, fieldName: string): void => {
  if (value === undefined || value === null || value === '') {
    throw createValidationError(fieldName, value, 'is required')
  }
}

export const isString = (value: any, fieldName: string): void => {
  if (typeof value !== 'string') {
    throw createValidationError(fieldName, value, 'must be a string')
  }
}

export const isNumber = (value: any, fieldName: string): void => {
  if (typeof value !== 'number' || isNaN(value)) {
    throw createValidationError(fieldName, value, 'must be a valid number')
  }
}

export const isPositiveNumber = (value: any, fieldName: string): void => {
  isNumber(value, fieldName)
  if (value <= 0) {
    throw createValidationError(fieldName, value, 'must be a positive number')
  }
}

export const isEmail = (value: string, fieldName: string): void => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(value)) {
    throw createValidationError(fieldName, value, 'must be a valid email address')
  }
}

export const isPhoneNumber = (value: string, fieldName: string): void => {
  // Basic phone number validation (supports international format)
  const phoneRegex = /^\+?[1-9]\d{1,14}$/
  if (!phoneRegex.test(value.replace(/[\s-()]/g, ''))) {
    throw createValidationError(fieldName, value, 'must be a valid phone number')
  }
}

export const isUrl = (value: string, fieldName: string): void => {
  try {
    new URL(value)
  } catch {
    throw createValidationError(fieldName, value, 'must be a valid URL')
  }
}

export const hasMaxLength = (value: string, maxLength: number, fieldName: string): void => {
  if (value.length > maxLength) {
    throw createValidationError(
      fieldName,
      value,
      `must not exceed ${maxLength} characters`
    )
  }
}

export const hasMinLength = (value: string, minLength: number, fieldName: string): void => {
  if (value.length < minLength) {
    throw createValidationError(
      fieldName,
      value,
      `must be at least ${minLength} characters`
    )
  }
}

export const isOneOf = <T>(value: T, allowedValues: T[], fieldName: string): void => {
  if (!allowedValues.includes(value)) {
    throw createValidationError(
      fieldName,
      value,
      `must be one of: ${allowedValues.join(', ')}`
    )
  }
}

export const isArray = (value: any, fieldName: string): void => {
  if (!Array.isArray(value)) {
    throw createValidationError(fieldName, value, 'must be an array')
  }
}

export const hasMaxItems = (value: any[], maxItems: number, fieldName: string): void => {
  if (value.length > maxItems) {
    throw createValidationError(
      fieldName,
      value,
      `must not have more than ${maxItems} items`
    )
  }
}

// Currency validation
export const isSupportedCurrency = (currency: string, fieldName: string): void => {
  const supportedCurrencies = ['INR', 'USD', 'EUR', 'GBP'] // Add more as needed
  isOneOf(currency, supportedCurrencies, fieldName)
}

// Amount validation with currency-specific rules
export const isValidAmount = (amount: number, currency: string, fieldName: string): void => {
  isPositiveNumber(amount, fieldName)
  
  // Currency-specific validation
  switch (currency) {
    case 'INR':
      if (amount < 1) {
        throw createValidationError(fieldName, amount, 'minimum amount is ₹1.00')
      }
      if (amount > 1000000) {
        throw createValidationError(fieldName, amount, 'maximum amount is ₹10,00,000.00')
      }
      break
    case 'USD':
      if (amount < 0.50) {
        throw createValidationError(fieldName, amount, 'minimum amount is $0.50')
      }
      if (amount > 10000) {
        throw createValidationError(fieldName, amount, 'maximum amount is $10,000.00')
      }
      break
    default:
      // Generic validation for other currencies
      if (amount < 0.01) {
        throw createValidationError(fieldName, amount, 'minimum amount is 0.01')
      }
  }
}

// Payment status validation
export const isValidPaymentStatus = (status: string, fieldName: string): void => {
  const validStatuses: PaymentStatus[] = [
    'created',
    'pending',
    'succeeded',
    'failed',
    'cancelled',
    'expired'
  ]
  isOneOf(status as PaymentStatus, validStatuses, fieldName)
}

// Invoice number validation
export const isValidInvoiceNumber = (invoiceNumber: string, fieldName: string): void => {
  isString(invoiceNumber, fieldName)
  hasMinLength(invoiceNumber, 3, fieldName)
  hasMaxLength(invoiceNumber, 50, fieldName)
  
  // Check for valid characters (alphanumeric, hyphens, underscores)
  const validPattern = /^[a-zA-Z0-9_-]+$/
  if (!validPattern.test(invoiceNumber)) {
    throw createValidationError(
      fieldName,
      invoiceNumber,
      'must contain only alphanumeric characters, hyphens, and underscores'
    )
  }
}

// Customer ID validation
export const isValidCustomerId = (customerId: string, fieldName: string): void => {
  isString(customerId, fieldName)
  hasMinLength(customerId, 3, fieldName)
  hasMaxLength(customerId, 100, fieldName)
}

// Meta data validation
export const isValidMetaData = (metaData: any[], fieldName: string): void => {
  isArray(metaData, fieldName)
  hasMaxItems(metaData, 10, fieldName)
  
  metaData.forEach((item, index) => {
    if (!item || typeof item !== 'object') {
      throw createValidationError(
        `${fieldName}[${index}]`,
        item,
        'must be an object with key and value properties'
      )
    }
    
    if (!item.key || typeof item.key !== 'string') {
      throw createValidationError(
        `${fieldName}[${index}].key`,
        item.key,
        'must be a non-empty string'
      )
    }
    
    if (!item.value || typeof item.value !== 'string') {
      throw createValidationError(
        `${fieldName}[${index}].value`,
        item.value,
        'must be a non-empty string'
      )
    }
    
    hasMaxLength(item.key, 50, `${fieldName}[${index}].key`)
    hasMaxLength(item.value, 200, `${fieldName}[${index}].value`)
  })
}

// Comprehensive payment request validation
export const validatePaymentRequest = (request: PaymentRequest): void => {
  // Required fields
  isRequired(request.amount, 'amount')
  isRequired(request.description, 'description')
  isRequired(request.invoice_number, 'invoice_number')
  isRequired(request.customer_id, 'customer_id')

  // Type and format validation
  isPositiveNumber(request.amount, 'amount')
  isString(request.description, 'description')
  isValidInvoiceNumber(request.invoice_number, 'invoice_number')
  isValidCustomerId(request.customer_id, 'customer_id')

  // Currency validation
  const currency = request.currency || 'INR'
  isSupportedCurrency(currency, 'currency')
  isValidAmount(request.amount, currency, 'amount')

  // Length validation
  hasMaxLength(request.description, 500, 'description')

  // Optional field validation
  if (request.customer_name) {
    isString(request.customer_name, 'customer_name')
    hasMaxLength(request.customer_name, 100, 'customer_name')
  }

  if (request.customer_email) {
    isString(request.customer_email, 'customer_email')
    isEmail(request.customer_email, 'customer_email')
  }

  if (request.customer_phone) {
    isString(request.customer_phone, 'customer_phone')
    isPhoneNumber(request.customer_phone, 'customer_phone')
  }

  if (request.redirect_url) {
    isString(request.redirect_url, 'redirect_url')
    isUrl(request.redirect_url, 'redirect_url')
  }

  if (request.reference_id) {
    isString(request.reference_id, 'reference_id')
    hasMaxLength(request.reference_id, 100, 'reference_id')
  }

  if (request.meta_data) {
    isValidMetaData(request.meta_data, 'meta_data')
  }
}

// Refund request validation
export const validateRefundRequest = (request: RefundRequest): void => {
  // Required fields
  isRequired(request.payment_id, 'payment_id')
  isRequired(request.amount, 'amount')

  // Type and format validation
  isString(request.payment_id, 'payment_id')
  isPositiveNumber(request.amount, 'amount')

  // Optional field validation
  if (request.reason) {
    isString(request.reason, 'reason')
    hasMaxLength(request.reason, 500, 'reason')
  }

  if (request.reference_id) {
    isString(request.reference_id, 'reference_id')
    hasMaxLength(request.reference_id, 100, 'reference_id')
  }
}

// Pagination validation
export const validatePaginationParams = (page?: number, limit?: number): void => {
  if (page !== undefined) {
    isNumber(page, 'page')
    if (page < 1) {
      throw createValidationError('page', page, 'must be greater than 0')
    }
  }

  if (limit !== undefined) {
    isNumber(limit, 'limit')
    if (limit < 1 || limit > 100) {
      throw createValidationError('limit', limit, 'must be between 1 and 100')
    }
  }
}

// Date validation
export const isValidDate = (dateString: string, fieldName: string): void => {
  const date = new Date(dateString)
  if (isNaN(date.getTime())) {
    throw createValidationError(fieldName, dateString, 'must be a valid date')
  }
}

export const isValidDateRange = (fromDate?: string, toDate?: string): void => {
  if (fromDate) {
    isValidDate(fromDate, 'from_date')
  }

  if (toDate) {
    isValidDate(toDate, 'to_date')
  }

  if (fromDate && toDate) {
    const from = new Date(fromDate)
    const to = new Date(toDate)
    
    if (from > to) {
      throw createValidationError(
        'date_range',
        { from_date: fromDate, to_date: toDate },
        'from_date must be before to_date'
      )
    }
  }
}

// Webhook validation
export const validateWebhookSignature = (
  payload: string,
  signature: string,
  secret: string
): boolean => {
  if (!secret) {
    return true // Skip validation if no secret is configured
  }

  try {
    const crypto = require('crypto')
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex')
    
    return signature === expectedSignature
  } catch (error) {
    return false
  }
}

// Sanitization functions
export const sanitizeString = (value: string): string => {
  return value.trim().replace(/[<>]/g, '')
}

export const sanitizeAmount = (amount: number): number => {
  return Math.round(amount * 100) / 100 // Round to 2 decimal places
}

export const sanitizePaymentRequest = (request: PaymentRequest): PaymentRequest => {
  return {
    ...request,
    description: sanitizeString(request.description),
    invoice_number: sanitizeString(request.invoice_number),
    customer_id: sanitizeString(request.customer_id),
    customer_name: request.customer_name ? sanitizeString(request.customer_name) : undefined,
    customer_email: request.customer_email ? sanitizeString(request.customer_email) : undefined,
    customer_phone: request.customer_phone ? sanitizeString(request.customer_phone) : undefined,
    reference_id: request.reference_id ? sanitizeString(request.reference_id) : undefined,
    amount: sanitizeAmount(request.amount),
  }
}

// Validation middleware for API routes
export const withValidation = <T>(
  validator: (data: T) => void,
  sanitizer?: (data: T) => T
) => {
  return (handler: Function) => {
    return async (request: Request, ...args: any[]) => {
      try {
        const body = await request.json()

        // Sanitize input if sanitizer is provided
        const sanitizedData = sanitizer ? sanitizer(body) : body

        // Validate input
        validator(sanitizedData)

        // Call the original handler with sanitized data
        const modifiedRequest = new Request(request.url, {
          ...request,
          body: JSON.stringify(sanitizedData)
        })

        return await handler(modifiedRequest, ...args)
      } catch (error) {
        if (error instanceof ValidationError) {
          return new Response(JSON.stringify(error.toJSON()), {
            status: error.statusCode,
            headers: { 'Content-Type': 'application/json' }
          })
        }
        throw error
      }
    }
  }
}
