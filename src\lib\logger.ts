// Comprehensive logging utility for payment service

import type { LogEntry } from '../types/service'

export type LogLevel = 'debug' | 'info' | 'warn' | 'error'

export interface LogContext {
  service?: string
  method?: string
  payment_session_id?: string
  customer_id?: string
  transaction_id?: string
  request_id?: string
  user_id?: string
  ip_address?: string
  user_agent?: string
  data?: any
  [key: string]: any
}

export class Logger {
  private serviceName: string
  private logLevel: LogLevel
  private enableConsole: boolean
  private enableFile: boolean

  constructor(
    serviceName: string = 'payment-service',
    logLevel: LogLevel = 'info',
    options: {
      enableConsole?: boolean
      enableFile?: boolean
    } = {}
  ) {
    this.serviceName = serviceName
    this.logLevel = logLevel
    this.enableConsole = options.enableConsole ?? true
    this.enableFile = options.enableFile ?? false
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3,
    }
    return levels[level] >= levels[this.logLevel]
  }

  private formatLogEntry(
    level: LogLevel,
    message: string,
    context?: LogContext,
    error?: Error
  ): LogEntry {
    return {
      timestamp: new Date(),
      level,
      message,
      context: {
        service: this.serviceName,
        ...context,
      },
      data: context?.data,
      error: error
        ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
          }
        : undefined,
    }
  }

  private writeLog(entry: LogEntry): void {
    if (this.enableConsole) {
      this.writeToConsole(entry)
    }

    if (this.enableFile) {
      this.writeToFile(entry)
    }

    // In production, you might want to send logs to external services
    // like CloudWatch, Datadog, Sentry, etc.
    this.writeToExternalService(entry)
  }

  private writeToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString()
    const level = entry.level.toUpperCase().padEnd(5)
    const service = entry.context?.service || 'unknown'
    const method = entry.context?.method || ''
    
    const prefix = `[${timestamp}] ${level} [${service}${method ? '::' + method : ''}]`
    const message = `${prefix} ${entry.message}`

    switch (entry.level) {
      case 'debug':
        console.debug(message, entry.context, entry.data)
        break
      case 'info':
        console.info(message, entry.context)
        break
      case 'warn':
        console.warn(message, entry.context, entry.data)
        break
      case 'error':
        console.error(message, entry.context, entry.error, entry.data)
        break
    }
  }

  private writeToFile(entry: LogEntry): void {
    // File logging implementation would go here
    // For now, we'll skip this as it requires file system access
  }

  private writeToExternalService(entry: LogEntry): void {
    // External service logging would go here
    // Examples: Sentry, CloudWatch, Datadog, etc.
    
    // For critical errors, you might want to send alerts
    if (entry.level === 'error' && entry.context?.payment_session_id) {
      this.sendAlert(entry)
    }
  }

  private sendAlert(entry: LogEntry): void {
    // Alert implementation would go here
    // For now, we'll just log it as a critical error
    console.error('CRITICAL ERROR ALERT:', {
      message: entry.message,
      payment_session_id: entry.context?.payment_session_id,
      customer_id: entry.context?.customer_id,
      error: entry.error,
      timestamp: entry.timestamp,
    })
  }

  debug(message: string, context?: LogContext): void {
    if (this.shouldLog('debug')) {
      const entry = this.formatLogEntry('debug', message, context)
      this.writeLog(entry)
    }
  }

  info(message: string, context?: LogContext): void {
    if (this.shouldLog('info')) {
      const entry = this.formatLogEntry('info', message, context)
      this.writeLog(entry)
    }
  }

  warn(message: string, context?: LogContext): void {
    if (this.shouldLog('warn')) {
      const entry = this.formatLogEntry('warn', message, context)
      this.writeLog(entry)
    }
  }

  error(message: string, error?: Error, context?: LogContext): void {
    if (this.shouldLog('error')) {
      const entry = this.formatLogEntry('error', message, context, error)
      this.writeLog(entry)
    }
  }

  // Convenience methods for common payment operations
  paymentCreated(paymentSessionId: string, amount: number, customerId: string): void {
    this.info('Payment session created', {
      method: 'createPayment',
      payment_session_id: paymentSessionId,
      customer_id: customerId,
      data: { amount },
    })
  }

  paymentCompleted(paymentSessionId: string, paymentId: string, status: string): void {
    this.info('Payment completed', {
      method: 'paymentCompleted',
      payment_session_id: paymentSessionId,
      data: { payment_id: paymentId, status },
    })
  }

  paymentFailed(paymentSessionId: string, error: Error, context?: LogContext): void {
    this.error('Payment failed', error, {
      method: 'paymentFailed',
      payment_session_id: paymentSessionId,
      ...context,
    })
  }

  refundCreated(refundId: string, paymentId: string, amount: number): void {
    this.info('Refund created', {
      method: 'createRefund',
      data: { refund_id: refundId, payment_id: paymentId, amount },
    })
  }

  webhookReceived(eventType: string, paymentSessionId: string): void {
    this.info('Webhook received', {
      method: 'webhookReceived',
      payment_session_id: paymentSessionId,
      data: { event_type: eventType },
    })
  }

  webhookProcessed(eventType: string, paymentSessionId: string, success: boolean): void {
    const message = success ? 'Webhook processed successfully' : 'Webhook processing failed'
    const logMethod = success ? this.info.bind(this) : this.warn.bind(this)
    
    logMethod(message, {
      method: 'webhookProcessed',
      payment_session_id: paymentSessionId,
      data: { event_type: eventType, success },
    })
  }

  apiRequest(method: string, url: string, statusCode?: number, duration?: number): void {
    this.info('API request', {
      method: 'apiRequest',
      data: {
        http_method: method,
        url,
        status_code: statusCode,
        duration_ms: duration,
      },
    })
  }

  databaseOperation(operation: string, collection: string, duration?: number): void {
    this.debug('Database operation', {
      method: 'databaseOperation',
      data: {
        operation,
        collection,
        duration_ms: duration,
      },
    })
  }

  authTokenRefreshed(expiresAt?: Date): void {
    this.info('Auth token refreshed', {
      method: 'refreshToken',
      data: { expires_at: expiresAt },
    })
  }

  authTokenExpired(): void {
    this.warn('Auth token expired', {
      method: 'checkToken',
    })
  }

  // Performance monitoring
  startTimer(): () => number {
    const startTime = Date.now()
    return () => Date.now() - startTime
  }

  // Request correlation
  withRequestId(requestId: string): Logger {
    const newLogger = new Logger(this.serviceName, this.logLevel, {
      enableConsole: this.enableConsole,
      enableFile: this.enableFile,
    })
    
    // Override the formatLogEntry method to include request ID
    const originalFormatLogEntry = newLogger.formatLogEntry.bind(newLogger)
    newLogger.formatLogEntry = (level, message, context, error) => {
      return originalFormatLogEntry(level, message, { ...context, request_id: requestId }, error)
    }
    
    return newLogger
  }

  // Structured logging for metrics
  metric(name: string, value: number, unit: string = 'count', tags?: Record<string, string>): void {
    this.info('Metric recorded', {
      method: 'metric',
      data: {
        metric_name: name,
        value,
        unit,
        tags,
      },
    })
  }

  // Security logging
  securityEvent(event: string, details: any, severity: 'low' | 'medium' | 'high' = 'medium'): void {
    this.warn('Security event', {
      method: 'securityEvent',
      data: {
        event,
        severity,
        details,
      },
    })
  }
}

// Default logger instance
export const logger = new Logger('payment-service', 
  (process.env.LOG_LEVEL as LogLevel) || 'info',
  {
    enableConsole: true,
    enableFile: process.env.NODE_ENV === 'production',
  }
)

// Request logging middleware
export const withRequestLogging = (handler: Function) => {
  return async (request: Request, ...args: any[]) => {
    const requestId = crypto.randomUUID()
    const requestLogger = logger.withRequestId(requestId)
    const timer = requestLogger.startTimer()
    
    const method = request.method
    const url = request.url
    
    requestLogger.info('Request started', {
      method: 'requestHandler',
      data: { http_method: method, url },
    })

    try {
      const response = await handler(request, ...args)
      const duration = timer()
      const statusCode = response.status
      
      requestLogger.info('Request completed', {
        method: 'requestHandler',
        data: { http_method: method, url, status_code: statusCode, duration_ms: duration },
      })
      
      return response
    } catch (error) {
      const duration = timer()
      
      requestLogger.error('Request failed', error as Error, {
        method: 'requestHandler',
        data: { http_method: method, url, duration_ms: duration },
      })
      
      throw error
    }
  }
}

// Performance monitoring decorator
export const withPerformanceLogging = (operationName: string) => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    const method = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const timer = logger.startTimer()
      
      try {
        const result = await method.apply(this, args)
        const duration = timer()
        
        logger.debug(`Operation completed: ${operationName}`, {
          method: operationName,
          data: { duration_ms: duration },
        })
        
        return result
      } catch (error) {
        const duration = timer()
        
        logger.error(`Operation failed: ${operationName}`, error as Error, {
          method: operationName,
          data: { duration_ms: duration },
        })
        
        throw error
      }
    }
    
    return descriptor
  }
}
