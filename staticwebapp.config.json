{"routes": [{"route": "/api/*", "allowedRoles": ["anonymous"], "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"]}], "navigationFallback": {"rewrite": "/index.html", "exclude": ["/api/*", "/_next/*", "/static/*"]}, "mimeTypes": {".json": "application/json"}, "globalHeaders": {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS", "Access-Control-Allow-Headers": "Content-Type, Authorization"}, "responseOverrides": {"404": {"rewrite": "/404.html"}}, "platform": {"apiRuntime": "node:18"}}