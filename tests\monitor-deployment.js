/**
 * Monitor Azure deployment and test when routing fix takes effect
 */

const fetch = globalThis.fetch || require('node-fetch')

const PRODUCTION_URL = 'https://yellow-sky-08e56d200.5.azurestaticapps.net'

async function testPaymentAPI() {
  const testData = {
    amount: 1,
    currency: 'INR',
    description: 'Deployment Monitor Test',
    invoice_number: `MONITOR_${Date.now()}`,
    customer_id: `monitor_${Date.now()}`,
  }

  try {
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testData)
    })

    const responseText = await response.text()
    
    // Check if we got documentation (routing issue) or payment session (fixed)
    const isDocumentation = responseText.includes('Payment Session Creation Requirements')
    const isPaymentSession = responseText.includes('payment_session_id') || responseText.includes('payments_session_id')
    
    return {
      status: response.status,
      isFixed: isPaymentSession,
      isRoutingIssue: isDocumentation,
      responsePreview: responseText.substring(0, 200)
    }
  } catch (error) {
    return {
      status: 'error',
      error: error.message
    }
  }
}

async function monitorDeployment() {
  console.log('🔍 Monitoring Azure Deployment for Routing Fix')
  console.log('==============================================')
  console.log(`URL: ${PRODUCTION_URL}`)
  console.log(`Started: ${new Date().toISOString()}`)
  console.log('==============================================\n')

  let attempts = 0
  const maxAttempts = 20 // 20 attempts = ~10 minutes
  const interval = 30000 // 30 seconds

  while (attempts < maxAttempts) {
    attempts++
    console.log(`🔄 Attempt ${attempts}/${maxAttempts} - Testing payment API...`)
    
    const result = await testPaymentAPI()
    
    if (result.isFixed) {
      console.log('🎉 SUCCESS! Routing fix is working!')
      console.log('✅ Payment API is now functional in production')
      console.log(`📊 Response Status: ${result.status}`)
      console.log(`📄 Response Preview: ${result.responsePreview}...`)
      return true
    } else if (result.isRoutingIssue) {
      console.log(`⏳ Still waiting... (routing issue persists)`)
      console.log(`📊 Status: ${result.status} - Still getting documentation`)
    } else if (result.error) {
      console.log(`❌ Error: ${result.error}`)
    } else {
      console.log(`❓ Unexpected response: ${result.responsePreview}`)
    }
    
    if (attempts < maxAttempts) {
      console.log(`⏰ Waiting ${interval/1000} seconds before next test...\n`)
      await new Promise(resolve => setTimeout(resolve, interval))
    }
  }
  
  console.log('⏰ Monitoring timeout reached')
  console.log('💡 The deployment might take longer than expected')
  console.log('🔗 Check GitHub Actions or Azure Portal for deployment status')
  return false
}

// Run the monitor
if (typeof require !== 'undefined' && require.main === module) {
  monitorDeployment()
    .then(success => {
      if (success) {
        console.log('\n🚀 Payment API is ready for production use!')
        process.exit(0)
      } else {
        console.log('\n⏰ Continue monitoring manually or check deployment logs')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('\n💥 Monitoring failed:', error.message)
      process.exit(1)
    })
}

module.exports = { monitorDeployment, testPaymentAPI }
