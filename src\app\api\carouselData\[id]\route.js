import connectedDB from '@/app/config/database'
import CarouselDate from '@/app/models/CarouselData'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { id: 'customer1' },
    { id: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/carouselData/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const carouselData = await CarouselDate.find({}).sort('sortOrder')

    if (!carouselData) return new Response(JSON.stringify({ results: [] }), { status: 200 })
    return new Response(JSON.stringify({ results: carouselData }), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
