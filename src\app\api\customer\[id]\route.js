import connectedDB from '@/app/config/database'
import Customer from '@/app/models/Customer'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { id: 'customer1' },
    { id: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/customer/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    // const customer = await Customer.findOne({
    //   customerId: params.id,
    // })

    const customerWithAddress = await Customer.aggregate([
      {
        $match: {
          customerId: params.id, // Use params.id consistently
        },
      },
      {
        $lookup: {
          from: 'CustomerAddresses', // Ensure collection name is correct
          localField: 'customerId', // Field in Customer collection
          foreignField: 'customerId', // Field in CustomerAddress collection
          as: 'addressDetails', // Name for the joined data
        },
      },
      {
        $unwind: {
          path: '$addressDetails', // Unwind the joined array
          preserveNullAndEmptyArrays: false, // Remove documents without a match
        },
      },
      {
        $project: {
          _id: 0,
          customerId: 1,
          customerName: 1,
          Email: 1,
          mobileNumber: 1,
          companyName: 1,
          gstNo: 1,

          bisinessVertical: 1,
          customerCode: 1,
          billingAddress: '$addressDetails.billingAddress', // Include billingAddress from joined data
        },
      },
      {
        $limit: 1, // Limit the output to 1 document
      },
    ])

    console.log('Customer with address:', customerWithAddress)

    if (!customerWithAddress) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify(customerWithAddress[0]), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
