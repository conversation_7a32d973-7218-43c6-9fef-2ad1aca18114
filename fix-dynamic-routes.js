const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// Find all route.js files in dynamic route folders
const routeFiles = execSync('find src/app -type d -name "[*]" -exec find {} -name "route.js*" \\;', {
  encoding: 'utf8',
})
  .split('\n')
  .filter(Boolean)

console.log(`Found ${routeFiles.length} dynamic route files:`)
routeFiles.forEach((file) => console.log(`- ${file}`))

// Add generateStaticParams to each file if it doesn't already have it
routeFiles.forEach((file) => {
  let content = fs.readFileSync(file, 'utf8')

  if (!content.includes('generateStaticParams')) {
    // Extract the parameter name from the path
    const dirName = path.basename(path.dirname(file))
    const paramName = dirName.replace(/[\[\]]/g, '')

    // Create the generateStaticParams function
    const staticParamsFunc = `
export async function generateStaticParams() {
  // Auto-generated for static export
  return [
    { ${paramName}: 'placeholder1' },
    { ${paramName}: 'placeholder2' },
  ]
}
`

    // Add the function to the beginning of the file
    content = staticParamsFunc + content
    fs.writeFileSync(file, content)
    console.log(`Added generateStaticParams to ${file}`)
  }
})

console.log('Done!')
