import connectedDB from '@/app/config/database'
import ProductCatalogue from '@/app/models/ProductCatalogue'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { id: 'customer1' },
    { id: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/productCatalogue/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const productCatalogue = await ProductCatalogue.find({ status: 'Active' }, { _id: 0 }).sort({ sortOrder: 1 })

    if (!productCatalogue) return new Response(JSON.stringify({ results: [] }), { status: 200 })
    return new Response(JSON.stringify({ results: productCatalogue }), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
