//import connectedDB from '@/config/database';
import connectedDB from '@/app/config/database'
import SMRReport from '@/app/models/SMRReport'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { customerId: 'customer1' },
    { customerId: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/smrReport/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const smrReport = await SMRReport.find({
      partnerId: params.customerId,
    })

    if (!smrReport) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify({ results: smrReport }), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
