/**
 * Enhanced Production Payment Service Test Suite
 *
 * Comprehensive testing of Zoho Payment integration in production environment
 * Tests payment session creation, status tracking, and all API endpoints
 */

// Import fetch for Node.js environments
const fetch = globalThis.fetch || require('node-fetch')

const PRODUCTION_URL = 'https://yellow-sky-08e56d200.5.azurestaticapps.net'
const LOCAL_URL = 'http://localhost:3000'

// Test configuration
const TEST_CONFIG = {
  // Use small amounts for production testing to avoid actual charges
  TEST_AMOUNT: 1.00, // ₹1 for production testing
  CURRENCY: 'INR',
  TIMEOUT: 30000, // 30 seconds timeout for API calls
  RETRY_ATTEMPTS: 3,
  DELAY_BETWEEN_TESTS: 2000, // 2 seconds delay between tests
}

/**
 * Test health check endpoint
 */
async function testHealthCheck(baseUrl = LOCAL_URL) {
  console.log(`\n=== Testing Health Check (${baseUrl}) ===`)

  try {
    const response = await fetch(`${baseUrl}/api/zoho/health`)
    const data = await response.json()

    console.log('Health check response:', JSON.stringify(data, null, 2))

    const isHealthy = data.status === 'healthy'
    const domainConfigured = data.configuration?.domain !== 'not configured'

    if (isHealthy && domainConfigured) {
      console.log('✅ Health check passed - Service is healthy and domain is configured')
      return { success: true, data }
    } else {
      console.log('❌ Health check failed')
      return { success: false, error: data }
    }
  } catch (error) {
    console.log('❌ Health check request failed:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Enhanced payment session creation test with comprehensive validation
 */
async function testPaymentSessionCreation(baseUrl = LOCAL_URL) {
  console.log(`\n=== Testing Payment Session Creation (${baseUrl}) ===`)

  const timestamp = Date.now()
  const testPaymentData = {
    amount: TEST_CONFIG.TEST_AMOUNT,
    currency: TEST_CONFIG.CURRENCY,
    description: 'Production Test Payment - Safe Amount',
    invoice_number: `PROD_TEST_${timestamp}`,
    customer_id: `test_customer_${timestamp}`,
    customer_name: 'Production Test Customer',
    customer_email: '<EMAIL>',
    customer_phone: '+91-**********',
    redirect_url: `${baseUrl}/payment/success`,
    reference_id: `PROD_REF_${timestamp}`,
    meta_data: [
      { key: 'test_mode', value: 'production_safe_test' },
      { key: 'environment', value: baseUrl.includes('localhost') ? 'local' : 'production' },
      { key: 'test_timestamp', value: timestamp.toString() },
      { key: 'test_amount', value: TEST_CONFIG.TEST_AMOUNT.toString() },
    ],
  }

  try {
    console.log('Creating payment session with data:', JSON.stringify(testPaymentData, null, 2))

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), TEST_CONFIG.TIMEOUT)

    const response = await fetch(`${baseUrl}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPaymentData),
      signal: controller.signal,
    })

    clearTimeout(timeoutId)
    const data = await response.json()

    console.log('Payment session creation response:', JSON.stringify(data, null, 2))

    // Enhanced validation
    if (response.ok && data.success) {
      const sessionData = data.payment_session || data.data?.payment_session

      if (sessionData && sessionData.payments_session_id) {
        console.log('✅ Payment session created successfully')
        console.log(`Payment Session ID: ${sessionData.payments_session_id}`)
        console.log(`Payment URL: ${sessionData.payment_url || 'Not provided'}`)
        console.log(`Session Status: ${sessionData.status || 'Unknown'}`)
        console.log(`Amount: ${sessionData.amount || testPaymentData.amount} ${sessionData.currency || testPaymentData.currency}`)

        return {
          success: true,
          data: {
            ...data,
            payment_session_id: sessionData.payments_session_id,
            payment_url: sessionData.payment_url,
            test_data: testPaymentData
          }
        }
      } else {
        console.log('❌ Payment session created but missing session data')
        return { success: false, error: 'Missing payment session data in response' }
      }
    } else {
      console.log('❌ Payment session creation failed')
      console.log(`Response status: ${response.status}`)
      console.log(`Response data:`, data)
      return { success: false, error: data, status: response.status }
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.log('❌ Payment session creation timed out')
      return { success: false, error: 'Request timeout' }
    }
    console.log('❌ Payment session creation request failed:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Enhanced payment status retrieval test
 */
async function testPaymentStatusRetrieval(baseUrl = LOCAL_URL, paymentSessionId = null) {
  console.log(`\n=== Testing Payment Status Retrieval (${baseUrl}) ===`)

  // Use a test payment session ID if none provided
  const testSessionId = paymentSessionId || 'test_session_id_123'
  console.log(`Testing with session ID: ${testSessionId}`)

  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), TEST_CONFIG.TIMEOUT)

    const response = await fetch(`${baseUrl}/api/zoho/payments/status/${testSessionId}`, {
      signal: controller.signal,
    })

    clearTimeout(timeoutId)
    const data = await response.json()

    console.log('Payment status response:', JSON.stringify(data, null, 2))

    if (response.ok) {
      console.log('✅ Payment status endpoint is accessible')

      // Additional validation for actual session data
      if (paymentSessionId && data.payment_session) {
        console.log(`Session Status: ${data.payment_session.status || 'Unknown'}`)
        console.log(`Session Amount: ${data.payment_session.amount || 'Unknown'}`)
        console.log(`Session Currency: ${data.payment_session.currency || 'Unknown'}`)
      }

      return { success: true, data }
    } else {
      console.log('❌ Payment status retrieval failed')
      console.log(`Response status: ${response.status}`)
      return { success: false, error: data, status: response.status }
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.log('❌ Payment status request timed out')
      return { success: false, error: 'Request timeout' }
    }
    console.log('❌ Payment status request failed:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Test webhook endpoint accessibility
 */
async function testWebhookEndpoint(baseUrl = LOCAL_URL) {
  console.log(`\n=== Testing Webhook Endpoint (${baseUrl}) ===`)

  try {
    const response = await fetch(`${baseUrl}/api/zoho/webhooks/payment`)
    const data = await response.json()

    console.log('Webhook endpoint response:', JSON.stringify(data, null, 2))

    if (response.ok && data.endpoint) {
      console.log('✅ Webhook endpoint is accessible')
      return { success: true, data }
    } else {
      console.log('❌ Webhook endpoint failed')
      return { success: false, error: data }
    }
  } catch (error) {
    console.log('❌ Webhook endpoint request failed:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Enhanced environment configuration test
 */
async function testEnvironmentConfiguration(baseUrl = LOCAL_URL) {
  console.log(`\n=== Testing Environment Configuration (${baseUrl}) ===`)

  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), TEST_CONFIG.TIMEOUT)

    const response = await fetch(`${baseUrl}/api/zoho/health`, {
      signal: controller.signal,
    })

    clearTimeout(timeoutId)
    const data = await response.json()

    const config = data.configuration || {}
    const checks = data.checks || {}

    console.log('Configuration Status:')
    console.log(`- Account ID: ${config.account_id}`)
    console.log(`- Webhook Secret: ${config.webhook_secret}`)
    console.log(`- Domain: ${config.domain}`)

    console.log('\nEnvironment Checks:')
    console.log(`- Database: ${checks.database?.status} - ${checks.database?.message || 'No message'}`)
    console.log(`- Environment Variables: ${checks.environment?.status} - ${checks.environment?.message || 'No message'}`)
    console.log(`- Zoho Auth: ${checks.zoho_auth?.status} - ${checks.zoho_auth?.message || 'No message'}`)
    console.log(`- Zoho API: ${checks.zoho_api?.status} - ${checks.zoho_api?.message || 'No message'}`)

    // Enhanced validation
    const configurationIssues = []
    const healthIssues = []

    if (config.account_id !== 'configured') configurationIssues.push('Account ID not configured')
    if (config.webhook_secret !== 'configured') configurationIssues.push('Webhook secret not configured')
    if (config.domain === 'not configured') configurationIssues.push('Domain not configured')

    Object.entries(checks).forEach(([key, check]) => {
      if (check.status !== 'healthy') {
        healthIssues.push(`${key}: ${check.status} - ${check.message || 'Unknown error'}`)
      }
    })

    const allConfigured = configurationIssues.length === 0
    const allHealthy = healthIssues.length === 0

    if (allConfigured && allHealthy) {
      console.log('✅ Environment configuration is complete and healthy')
      return { success: true, data }
    } else {
      console.log('❌ Environment configuration issues detected')
      if (configurationIssues.length > 0) {
        console.log('Configuration Issues:', configurationIssues)
      }
      if (healthIssues.length > 0) {
        console.log('Health Issues:', healthIssues)
      }
      return {
        success: false,
        error: data,
        issues: {
          configuration: configurationIssues,
          health: healthIssues
        }
      }
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.log('❌ Environment configuration check timed out')
      return { success: false, error: 'Request timeout' }
    }
    console.log('❌ Environment configuration check failed:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Test database connectivity and token availability
 */
async function testDatabaseAndTokens(baseUrl = LOCAL_URL) {
  console.log(`\n=== Testing Database Connectivity and Token Management (${baseUrl}) ===`)

  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), TEST_CONFIG.TIMEOUT)

    const response = await fetch(`${baseUrl}/api/zoho/health`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ include_sensitive: false }),
      signal: controller.signal,
    })

    clearTimeout(timeoutId)
    const data = await response.json()

    console.log('Database and Token Diagnostics:', JSON.stringify(data.diagnostics, null, 2))

    const dbStatus = data.diagnostics?.database?.status
    const tokenStatus = data.diagnostics?.tokens?.status
    const transactionStatus = data.diagnostics?.transactions?.status

    console.log(`Database Status: ${dbStatus}`)
    console.log(`Token Status: ${tokenStatus}`)
    console.log(`Transaction System Status: ${transactionStatus}`)

    if (data.diagnostics?.database?.collections) {
      console.log(`Available Collections: ${data.diagnostics.database.collections.join(', ')}`)
    }

    if (data.diagnostics?.transactions?.total_transactions !== undefined) {
      console.log(`Total Transactions: ${data.diagnostics.transactions.total_transactions}`)
      console.log(`Recent Transactions (24h): ${data.diagnostics.transactions.recent_transactions_24h}`)
    }

    const allHealthy = dbStatus === 'healthy' && tokenStatus === 'healthy' && transactionStatus === 'healthy'

    if (allHealthy) {
      console.log('✅ Database connectivity and token management are healthy')
      return { success: true, data }
    } else {
      console.log('❌ Issues detected with database or token management')
      return { success: false, error: data }
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.log('❌ Database and token test timed out')
      return { success: false, error: 'Request timeout' }
    }
    console.log('❌ Database and token test failed:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Test complete payment flow with session creation and status check
 */
async function testCompletePaymentFlow(baseUrl = LOCAL_URL) {
  console.log(`\n=== Testing Complete Payment Flow (${baseUrl}) ===`)

  try {
    // Step 1: Create payment session
    console.log('Step 1: Creating payment session...')
    const sessionResult = await testPaymentSessionCreation(baseUrl)

    if (!sessionResult.success) {
      console.log('❌ Payment flow failed at session creation')
      return { success: false, error: 'Session creation failed', details: sessionResult }
    }

    const paymentSessionId = sessionResult.data.payment_session_id
    console.log(`✅ Session created with ID: ${paymentSessionId}`)

    // Step 2: Wait a moment for session to be processed
    console.log('Step 2: Waiting for session processing...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    // Step 3: Check payment status
    console.log('Step 3: Checking payment status...')
    const statusResult = await testPaymentStatusRetrieval(baseUrl, paymentSessionId)

    if (!statusResult.success) {
      console.log('❌ Payment flow failed at status retrieval')
      return {
        success: false,
        error: 'Status retrieval failed',
        details: { sessionResult, statusResult },
        payment_session_id: paymentSessionId
      }
    }

    console.log('✅ Complete payment flow test successful')
    return {
      success: true,
      data: {
        session: sessionResult.data,
        status: statusResult.data,
        payment_session_id: paymentSessionId
      }
    }

  } catch (error) {
    console.log('❌ Payment flow test failed:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Enhanced production test runner with comprehensive coverage
 */
async function runProductionTests() {
  console.log('🚀 Starting Enhanced Production Payment Service Tests')
  console.log('=====================================================')
  console.log(`Test Configuration:`)
  console.log(`- Test Amount: ₹${TEST_CONFIG.TEST_AMOUNT}`)
  console.log(`- Currency: ${TEST_CONFIG.CURRENCY}`)
  console.log(`- Timeout: ${TEST_CONFIG.TIMEOUT}ms`)
  console.log(`- Production URL: ${PRODUCTION_URL}`)
  console.log('=====================================================')

  const tests = [
    // Basic health and configuration tests
    { name: 'Production Health Check', fn: () => testHealthCheck(PRODUCTION_URL), critical: true },
    { name: 'Production Environment Config', fn: () => testEnvironmentConfiguration(PRODUCTION_URL), critical: true },
    { name: 'Production Database & Tokens', fn: () => testDatabaseAndTokens(PRODUCTION_URL), critical: true },

    // API endpoint tests
    { name: 'Production Webhook Endpoint', fn: () => testWebhookEndpoint(PRODUCTION_URL), critical: false },

    // Payment functionality tests
    { name: 'Production Payment Session Creation', fn: () => testPaymentSessionCreation(PRODUCTION_URL), critical: true },
    { name: 'Production Payment Status Retrieval', fn: () => testPaymentStatusRetrieval(PRODUCTION_URL), critical: false },

    // Complete flow test
    { name: 'Production Complete Payment Flow', fn: () => testCompletePaymentFlow(PRODUCTION_URL), critical: true },

    // Local environment comparison tests (if available)
    { name: 'Local Health Check (Comparison)', fn: () => testHealthCheck(LOCAL_URL), critical: false },
    { name: 'Local Environment Config (Comparison)', fn: () => testEnvironmentConfiguration(LOCAL_URL), critical: false },
  ]

  let passed = 0
  let failed = 0
  let criticalFailed = 0
  const results = {}
  const startTime = Date.now()

  console.log(`\nRunning ${tests.length} tests...\n`)

  for (const test of tests) {
    const testStartTime = Date.now()
    console.log(`Running: ${test.name}...`)

    try {
      const result = await test.fn()
      const testDuration = Date.now() - testStartTime

      results[test.name] = { ...result, duration: testDuration, critical: test.critical }

      if (result.success) {
        passed++
        console.log(`✅ ${test.name} (${testDuration}ms)`)
      } else {
        failed++
        if (test.critical) criticalFailed++
        console.log(`❌ ${test.name} (${testDuration}ms)`)
      }
    } catch (error) {
      const testDuration = Date.now() - testStartTime
      console.log(`❌ ${test.name} threw an error: ${error.message} (${testDuration}ms)`)
      results[test.name] = {
        success: false,
        error: error.message,
        duration: testDuration,
        critical: test.critical
      }
      failed++
      if (test.critical) criticalFailed++
    }

    // Delay between tests
    if (tests.indexOf(test) < tests.length - 1) {
      await new Promise((resolve) => setTimeout(resolve, TEST_CONFIG.DELAY_BETWEEN_TESTS))
    }
  }

  const totalDuration = Date.now() - startTime

  console.log('\n=====================================================')
  console.log('🚀 ENHANCED PRODUCTION TEST SUMMARY')
  console.log('=====================================================')
  console.log(`Total Tests: ${tests.length}`)
  console.log(`Passed: ${passed}`)
  console.log(`Failed: ${failed}`)
  console.log(`Critical Failed: ${criticalFailed}`)
  console.log(`Success Rate: ${((passed / tests.length) * 100).toFixed(1)}%`)
  console.log(`Total Duration: ${(totalDuration / 1000).toFixed(2)}s`)

  // Detailed results
  console.log('\n📊 Detailed Results:')
  Object.entries(results).forEach(([testName, result]) => {
    const status = result.success ? '✅' : '❌'
    const critical = result.critical ? '[CRITICAL]' : '[OPTIONAL]'
    const duration = result.duration ? `(${result.duration}ms)` : ''
    console.log(`${status} ${critical} ${testName} ${duration}`)

    if (!result.success && result.error) {
      console.log(`    Error: ${typeof result.error === 'string' ? result.error : JSON.stringify(result.error)}`)
    }
  })

  if (criticalFailed === 0) {
    console.log('\n🎉 All critical tests passed!')
    console.log('✨ Payment service is ready for production use.')

    if (failed > 0) {
      console.log(`⚠️  ${failed} optional tests failed, but core functionality is working.`)
    }
  } else {
    console.log('\n🚨 Critical tests failed!')
    console.log('❌ Payment service has issues that need to be resolved before production use.')
  }

  return {
    passed,
    failed,
    criticalFailed,
    total: tests.length,
    results,
    duration: totalDuration,
    productionReady: criticalFailed === 0
  }
}

/**
 * Run production-only tests (focused on production environment)
 */
async function runProductionOnlyTests() {
  console.log('🎯 Starting Production-Only Payment Service Tests')
  console.log('================================================')
  console.log(`Production URL: ${PRODUCTION_URL}`)
  console.log(`Test Amount: ₹${TEST_CONFIG.TEST_AMOUNT} (Safe for production)`)
  console.log('================================================')

  const productionTests = [
    { name: 'Production Health Check', fn: () => testHealthCheck(PRODUCTION_URL), critical: true },
    { name: 'Production Environment Config', fn: () => testEnvironmentConfiguration(PRODUCTION_URL), critical: true },
    { name: 'Production Database & Tokens', fn: () => testDatabaseAndTokens(PRODUCTION_URL), critical: true },
    { name: 'Production Payment Session Creation', fn: () => testPaymentSessionCreation(PRODUCTION_URL), critical: true },
    { name: 'Production Complete Payment Flow', fn: () => testCompletePaymentFlow(PRODUCTION_URL), critical: true },
    { name: 'Production Webhook Endpoint', fn: () => testWebhookEndpoint(PRODUCTION_URL), critical: false },
  ]

  let passed = 0
  let failed = 0
  let criticalFailed = 0
  const results = {}
  const startTime = Date.now()

  console.log(`\nRunning ${productionTests.length} production tests...\n`)

  for (const test of productionTests) {
    const testStartTime = Date.now()
    console.log(`Running: ${test.name}...`)

    try {
      const result = await test.fn()
      const testDuration = Date.now() - testStartTime

      results[test.name] = { ...result, duration: testDuration, critical: test.critical }

      if (result.success) {
        passed++
        console.log(`✅ ${test.name} (${testDuration}ms)`)
      } else {
        failed++
        if (test.critical) criticalFailed++
        console.log(`❌ ${test.name} (${testDuration}ms)`)
      }
    } catch (error) {
      const testDuration = Date.now() - testStartTime
      console.log(`❌ ${test.name} threw an error: ${error.message} (${testDuration}ms)`)
      results[test.name] = {
        success: false,
        error: error.message,
        duration: testDuration,
        critical: test.critical
      }
      failed++
      if (test.critical) criticalFailed++
    }

    // Delay between tests
    if (productionTests.indexOf(test) < productionTests.length - 1) {
      await new Promise((resolve) => setTimeout(resolve, TEST_CONFIG.DELAY_BETWEEN_TESTS))
    }
  }

  const totalDuration = Date.now() - startTime

  console.log('\n================================================')
  console.log('🎯 PRODUCTION-ONLY TEST SUMMARY')
  console.log('================================================')
  console.log(`Total Tests: ${productionTests.length}`)
  console.log(`Passed: ${passed}`)
  console.log(`Failed: ${failed}`)
  console.log(`Critical Failed: ${criticalFailed}`)
  console.log(`Success Rate: ${((passed / productionTests.length) * 100).toFixed(1)}%`)
  console.log(`Total Duration: ${(totalDuration / 1000).toFixed(2)}s`)

  if (criticalFailed === 0) {
    console.log('\n🎉 Production environment is ready!')
    console.log('✨ All critical payment functionality is working.')
  } else {
    console.log('\n🚨 Production environment has critical issues!')
    console.log('❌ Payment service needs fixes before production use.')
  }

  return {
    passed,
    failed,
    criticalFailed,
    total: productionTests.length,
    results,
    duration: totalDuration,
    productionReady: criticalFailed === 0
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runProductionTests,
    runProductionOnlyTests,
    testHealthCheck,
    testPaymentSessionCreation,
    testPaymentStatusRetrieval,
    testWebhookEndpoint,
    testEnvironmentConfiguration,
    testDatabaseAndTokens,
    testCompletePaymentFlow,
    PRODUCTION_URL,
    LOCAL_URL,
    TEST_CONFIG,
  }
}

// Run tests if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  // Check command line arguments
  const args = process.argv.slice(2)

  if (args.includes('--production-only') || args.includes('-p')) {
    console.log('Running production-only tests...')
    runProductionOnlyTests().catch(console.error)
  } else {
    console.log('Running comprehensive tests (use --production-only for production-only tests)...')
    runProductionTests().catch(console.error)
  }
}
