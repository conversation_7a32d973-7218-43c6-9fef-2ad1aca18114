/**
 * Debug script to test payment API endpoints
 */

const fetch = globalThis.fetch || require('node-fetch')

const PRODUCTION_URL = 'https://yellow-sky-08e56d200.5.azurestaticapps.net'

async function debugPaymentAPI() {
  console.log('🔍 Debugging Payment API Endpoints')
  console.log('==================================')

  // Test 1: GET request to payment session endpoint (should return documentation)
  console.log('\n1. Testing GET request to payment session endpoint...')
  try {
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`)
    const data = await response.json()
    console.log(`Status: ${response.status}`)
    console.log(`Response:`, JSON.stringify(data, null, 2))
  } catch (error) {
    console.log('Error:', error.message)
  }

  // Test 2: POST request to health endpoint
  console.log('\n2. Testing POST request to health endpoint...')
  try {
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/health`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ include_sensitive: false }),
    })
    const data = await response.json()
    console.log(`Status: ${response.status}`)
    console.log(`Response:`, JSON.stringify(data, null, 2))
  } catch (error) {
    console.log('Error:', error.message)
  }

  // Test 3: POST request to payment session endpoint
  console.log('\n3. Testing POST request to payment session endpoint...')
  const testPaymentData = {
    amount: 1,
    currency: 'INR',
    description: 'Debug Test Payment',
    invoice_number: `DEBUG_${Date.now()}`,
    customer_id: `debug_customer_${Date.now()}`,
    customer_name: 'Debug Test Customer',
    customer_email: '<EMAIL>',
    customer_phone: '+91-**********',
  }

  try {
    console.log('Sending payload:', JSON.stringify(testPaymentData, null, 2))
    
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPaymentData),
    })
    
    console.log(`Status: ${response.status}`)
    console.log(`Headers:`, Object.fromEntries(response.headers.entries()))
    
    const responseText = await response.text()
    console.log(`Raw Response:`, responseText)
    
    try {
      const data = JSON.parse(responseText)
      console.log(`Parsed Response:`, JSON.stringify(data, null, 2))
    } catch (parseError) {
      console.log('Failed to parse response as JSON:', parseError.message)
    }
  } catch (error) {
    console.log('Error:', error.message)
  }

  // Test 4: Check if the endpoint exists by testing different methods
  console.log('\n4. Testing different HTTP methods...')
  const methods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
  
  for (const method of methods) {
    try {
      const response = await fetch(`${PRODUCTION_URL}/api/zoho/payments/create-session`, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: method === 'POST' || method === 'PUT' ? JSON.stringify(testPaymentData) : undefined,
      })
      
      console.log(`${method}: Status ${response.status}`)
    } catch (error) {
      console.log(`${method}: Error - ${error.message}`)
    }
  }
}

// Run the debug script
debugPaymentAPI().catch(console.error)
