import connectedDB from '@/app/config/database'
import Scheme from '@/app/models/SchemeQ3'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { customerId: 'customer1' },
    { customerId: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/scheme/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()

    const schemeData = await Scheme.findOne({ customerId: params.customerId }, { _id: 0 })

    if (!schemeData) return new Response(JSON.stringify({ results: null }), { status: 200 })
    return new Response(JSON.stringify({ results: schemeData }), { status: 200 })
  } catch (error) {
    return new Response(error, { status: 500 })
  }
}
