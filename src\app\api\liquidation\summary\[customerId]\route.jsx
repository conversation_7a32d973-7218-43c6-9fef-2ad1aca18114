import connectedDB from '@/app/config/database'
import Liquidation from '@/app/models/Liquidation'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { customerId: 'customer1' },
    { customerId: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/liquidation/summary/:customerId
export const GET = async (_, { params }) => {
  try {
    // Connect to the database
    await connectedDB()

    // Single optimized aggregation pipeline
    const result = await Liquidation.aggregate([
      // Stage 1: Match documents for the specific customer
      {
        $match: {
          customerId: params.customerId,
        },
      },

      // Stage 2: Ensure total is a number and extract year/month
      {
        $addFields: {
          total: {
            $cond: {
              if: { $eq: [{ $type: '$total' }, 'missing'] },
              then: 0,
              else: {
                $cond: {
                  if: { $eq: [{ $type: '$total' }, 'number'] },
                  then: '$total',
                  else: { $toDouble: { $ifNull: ['$total', 0] } },
                },
              },
            },
          },
          year: { $toString: { $year: '$date' } },
          month: { $month: '$date' },
        },
      },

      // Stage 3: Calculate total liquidation and group by year/month in a facet
      {
        $facet: {
          // Get total liquidation amount
          totalLiquidation: [
            {
              $group: {
                _id: null,
                total: { $sum: '$total' },
              },
            },
          ],

          // Get year/month breakdown
          yearMonthData: [
            // Group by year and month
            {
              $group: {
                _id: {
                  year: '$year',
                  month: '$month',
                },
                monthlyTotal: { $sum: '$total' },
              },
            },

            // Sort by year and month
            {
              $sort: {
                '_id.year': 1,
                '_id.month': 1,
              },
            },

            // Group by year to create the months object
            {
              $group: {
                _id: '$_id.year',
                months: {
                  $push: {
                    k: { $toString: '$_id.month' },
                    v: '$monthlyTotal',
                  },
                },
              },
            },

            // Convert the months array to an object
            {
              $project: {
                _id: 0,
                year: '$_id',
                months: { $arrayToObject: '$months' },
              },
            },

            // Sort by year
            {
              $sort: {
                year: 1,
              },
            },
          ],
        },
      },

      // Stage 4: Format the final response
      {
        $project: {
          customerId: params.customerId,
          liquidation: {
            totalLiquidation: {
              $cond: {
                if: { $eq: [{ $size: '$totalLiquidation' }, 0] },
                then: 0,
                else: { $arrayElemAt: ['$totalLiquidation.total', 0] },
              },
            },
            liquidationByYear: '$yearMonthData',
          },
        },
      },
    ])

    // Return the first (and only) result from the aggregation
    return new Response(
      JSON.stringify(
        result.length > 0
          ? result[0]
          : {
              customerId: params.customerId,
              liquidation: {
                totalLiquidation: 0,
                liquidationByYear: [],
              },
            }
      ),
      { status: 200 }
    )
  } catch (error) {
    console.error('Error in liquidation summary API:', error)
    return new Response(JSON.stringify({ error: 'Internal Server Error', details: error.message }), { status: 500 })
  }
}
