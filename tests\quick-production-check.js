/**
 * Quick Production Environment Check
 * 
 * Simple script to verify production domain configuration
 */

// Import fetch for Node.js environments
const fetch = globalThis.fetch || require('node-fetch')

const PRODUCTION_URL = 'https://yellow-sky-08e56d200.5.azurestaticapps.net'

async function quickProductionCheck() {
  console.log('🔍 Quick Production Environment Check')
  console.log('=====================================')
  console.log(`Testing: ${PRODUCTION_URL}`)
  console.log('')

  try {
    console.log('⏳ Checking health endpoint...')
    const response = await fetch(`${PRODUCTION_URL}/api/zoho/health`)
    
    if (!response.ok) {
      console.log(`❌ HTTP Error: ${response.status} ${response.statusText}`)
      return
    }

    const data = await response.json()
    
    console.log('📊 Health Check Results:')
    console.log(`   Status: ${data.status}`)
    console.log(`   Service: ${data.service}`)
    console.log(`   Version: ${data.version}`)
    console.log('')
    
    console.log('🔧 Configuration Status:')
    const config = data.configuration || {}
    console.log(`   Account ID: ${config.account_id}`)
    console.log(`   Webhook Secret: ${config.webhook_secret}`)
    console.log(`   Domain: ${config.domain}`)
    console.log('')
    
    console.log('🏥 Health Checks:')
    const checks = data.checks || {}
    Object.entries(checks).forEach(([name, check]) => {
      const icon = check.status === 'healthy' ? '✅' : '❌'
      console.log(`   ${icon} ${name}: ${check.status}`)
    })
    console.log('')
    
    // Overall assessment
    const domainConfigured = config.domain !== 'not configured'
    const allHealthy = Object.values(checks).every(check => check.status === 'healthy')
    const overallHealthy = data.status === 'healthy'
    
    if (overallHealthy && allHealthy && domainConfigured) {
      console.log('🎉 SUCCESS: Production environment is fully configured!')
      console.log('✨ Domain configuration: RESOLVED')
      console.log('✨ All health checks: PASSING')
      console.log('✨ Payment service: READY FOR USE')
    } else {
      console.log('⚠️  ISSUES DETECTED:')
      if (!overallHealthy) console.log('   - Overall health status is not healthy')
      if (!allHealthy) console.log('   - Some health checks are failing')
      if (!domainConfigured) console.log('   - Domain is not configured (NEXT_PUBLIC_DOMAIN secret missing)')
      
      console.log('')
      console.log('📋 Next Steps:')
      if (!domainConfigured) {
        console.log('   1. Set NEXT_PUBLIC_DOMAIN secret in GitHub repository')
        console.log('   2. Value: https://yellow-sky-08e56d200.5.azurestaticapps.net')
        console.log('   3. Trigger new deployment by pushing to main branch')
      }
    }
    
  } catch (error) {
    console.log('❌ Failed to connect to production environment')
    console.log(`   Error: ${error.message}`)
    console.log('')
    console.log('🔍 Possible causes:')
    console.log('   - Production site is not deployed')
    console.log('   - Network connectivity issues')
    console.log('   - API endpoints are not available')
  }
  
  console.log('')
  console.log('=====================================')
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { quickProductionCheck, PRODUCTION_URL }
}

// Run check if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  quickProductionCheck().catch(console.error)
}
