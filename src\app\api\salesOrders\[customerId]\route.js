import connectedDB from '@/app/config/database'
import SalesOrders from '@/app/models/SalesOrders'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { customerId: 'customer1' },
    { customerId: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/salesOrders/:customerId?groupBy=invoicedStatus&otherParam=value
export const GET = async (request, { params }) => {
  try {
    await connectedDB()

    const salesOrdersWithItems = await SalesOrders.aggregate([
      { $match: { customerId: params.customerId } },
      {
        $lookup: {
          from: 'SaleOrderItems',
          localField: 'salesOrderId',
          foreignField: 'salesOrderId',
          as: 'items',
        },
      },
      {
        $project: {
          _id: 0, // Exclude MongoDB _id
          salesOrderId: 1,
          salesOrderNumber: 1,
          customerId: 1,
          subTotal: 1,
          invoiceDate: 1,
          total: 1,
          createdTime: 1,
          lastModifiedTime: 1,
          invoicedStatus: 1,
          paidStatus: 1,
          salesChannel: 1,
          paymentTermsLabel: 1,
          orderSource: 1,
          saleOrderDate: 1,
          addressId: 1,
          items: 1, // Include the joined items
        },
      },
      { $sort: { createdTime: -1 } },
    ])

    if (!salesOrdersWithItems || salesOrdersWithItems.length === 0)
      return new Response(JSON.stringify({ results: [] }), { status: 200 })
    return new Response(JSON.stringify({ results: salesOrdersWithItems }), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
