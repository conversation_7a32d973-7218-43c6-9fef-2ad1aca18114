import connectedDB from '@/app/config/database'
import Liquidation from '@/app/models/Liquidation'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { customerId: 'customer1' },
    { customerId: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/liquidation/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()

    // Get the URL parameters
    const url = new URL(request.url)
    const aggregateByYear = url.searchParams.get('aggregateByYear')

    // If aggregateByYear parameter is present, return yearly totals
    if (aggregateByYear === 'true') {
      const liquidationByYear = await Liquidation.aggregate([
        {
          // Match documents for the specific customer
          $match: {
            customerId: params.customerId,
          },
        },
        {
          // Add a new field with the year extracted from the date
          $addFields: {
            year: { $year: '$date' },
          },
        },
        {
          // Group by year and calculate totals
          $group: {
            _id: '$year',
            totalAmount: { $sum: '$total' },
            totalProducts: { $sum: '$noOfProducts' },
            count: { $sum: 1 },
          },
        },
        {
          // Sort by year in ascending order
          $sort: {
            _id: 1,
          },
        },
        {
          // Project to rename fields for better readability
          $project: {
            _id: 0,
            year: '$_id',
            totalAmount: 1,
            totalProducts: 1,
            count: 1,
          },
        },
      ])

      return new Response(
        JSON.stringify({
          results: liquidationByYear,
          message: 'Liquidation data aggregated by year',
        }),
        { status: 200 }
      )
    }
    // Otherwise return all liquidation records for the customer
    else {
      const liquidation = await Liquidation.find({ customerId: params.customerId })

      if (!liquidation || liquidation.length === 0) {
        return new Response(JSON.stringify({ results: [] }), { status: 200 })
      }

      return new Response(JSON.stringify({ results: liquidation }), { status: 200 })
    }
  } catch (error) {
    console.error('Error in liquidation API:', error)
    return new Response(JSON.stringify({ error: 'Internal Server Error', details: error.message }), { status: 500 })
  }
}
