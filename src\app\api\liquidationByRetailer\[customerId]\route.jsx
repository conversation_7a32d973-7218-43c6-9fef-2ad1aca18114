import connectedDB from '@/app/config/database'
import LiquidationByRetailer from '@/app/models/LiquidationByRetailer'

export async function generateStaticParams() {
  // Return all possible customer IDs that this route should handle
  // For example:
  return [
    { customerId: 'customer1' },
    { customerId: 'customer2' },
    // Add all other customer IDs you need to support
  ]
}

// GET /api/liquidationByRetailer/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const liquidation = await LiquidationByRetailer.findOne({ partnerId: params.customerId })

    if (!liquidation) return new Response(JSON.stringify({ results: [] }), { status: 200 })
    return new Response(JSON.stringify({ results: liquidation }), { status: 200 })
  } catch (error) {
    return new Response(error, { status: 500 })
  }
}
